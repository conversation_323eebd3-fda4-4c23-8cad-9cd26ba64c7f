# 邮箱验证码自动处理系统

## 项目概述

这是一个自动生成临时邮箱并接收特定应用验证码的系统。系统利用域名邮箱转发功能，将验证码邮件转发到Gmail，然后自动提取验证码。

## 功能特性

- 🔄 自动生成唯一的临时邮箱地址
- 📧 实时监听Gmail收件箱
- 🔍 智能提取验证码
- 🌐 RESTful API接口
- 📊 Web管理界面
- 🐳 Docker容器化部署

## 系统架构

```
特定应用 → 自动生成邮箱@域名 → 邮箱转发 → Gmail → 验证码提取 → API返回
```

## 项目结构

```
email_verification_system/
├── src/                        # 源代码目录
│   ├── __init__.py
│   ├── email_generator.py      # 邮箱生成器
│   ├── gmail_monitor.py        # Gmail监听服务
│   ├── code_extractor.py       # 验证码提取器
│   ├── database.py             # 数据库操作
│   ├── api_server.py           # API服务器
│   └── models/                 # 数据模型
├── config/                     # 配置文件目录
│   ├── settings.py             # 系统配置
│   └── gmail_credentials.json  # Gmail API凭证
├── tests/                      # 测试文件
├── web/                        # Web界面
├── requirements.txt            # Python依赖
├── docker-compose.yml          # Docker部署配置
└── README.md                   # 项目说明
```

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置Gmail API

1. 在Google Cloud Console创建项目
2. 启用Gmail API
3. 创建服务账号并下载凭证文件
4. 将凭证文件保存为 `config/gmail_credentials.json`

### 3. 运行系统

```bash
# 启动API服务器
python src/api_server.py

# 启动Gmail监听服务
python src/gmail_monitor.py
```

## API接口

### 生成邮箱

```http
POST /api/generate-email
Content-Type: application/json

{
    "app_name": "example_app",
    "prefix": "test"
}
```

### 查询验证码

```http
GET /api/get-code/{email_id}
```

## 配置说明

在 `config/settings.py` 中配置：

- Gmail API凭证路径
- 数据库连接信息
- 邮箱生成规则
- 验证码提取模式

## 部署

使用Docker Compose一键部署：

```bash
docker-compose up -d
```

## 许可证

MIT License
