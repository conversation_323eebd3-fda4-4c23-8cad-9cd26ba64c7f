#!/usr/bin/env python3
"""
快速Gmail API测试
"""

import os
import json
from pathlib import Path

def test_credentials_file():
    """测试凭据文件"""
    print("📁 检查Gmail凭据文件...")
    
    creds_file = Path("config/gmail_credentials.json")
    
    if not creds_file.exists():
        print("❌ 凭据文件不存在")
        return False
    
    try:
        with open(creds_file, 'r', encoding='utf-8') as f:
            creds = json.load(f)
        
        if 'installed' in creds:
            client_id = creds['installed'].get('client_id', '')
            project_id = creds['installed'].get('project_id', '')
            
            print(f"✅ 凭据文件读取成功")
            print(f"   客户端ID: {client_id}")
            print(f"   项目ID: {project_id}")
            
            if client_id.startswith('733279784952-'):
                print("✅ 客户端ID匹配")
                return True
            else:
                print("❌ 客户端ID不匹配")
                return False
        else:
            print("❌ 凭据文件格式错误")
            return False
            
    except Exception as e:
        print(f"❌ 读取凭据文件失败: {e}")
        return False

def test_simple_auth():
    """简单的认证测试"""
    print("\n🔐 尝试Gmail API认证...")
    
    try:
        from google.auth.transport.requests import Request
        from google.oauth2.credentials import Credentials
        from google_auth_oauthlib.flow import InstalledAppFlow
        from googleapiclient.discovery import build
        
        SCOPES = ['https://www.googleapis.com/auth/gmail.readonly']
        creds_file = "config/gmail_credentials.json"
        token_file = "config/gmail_token.json"
        
        creds = None
        
        # 检查是否存在token文件
        if os.path.exists(token_file):
            creds = Credentials.from_authorized_user_file(token_file, SCOPES)
        
        # 如果没有有效凭证，进行OAuth流程
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                print("🔄 刷新访问令牌...")
                creds.refresh(Request())
            else:
                print("🌐 启动OAuth授权流程...")
                print("⚠️  浏览器将打开，请使用 <EMAIL> 登录")
                
                flow = InstalledAppFlow.from_client_secrets_file(creds_file, SCOPES)
                creds = flow.run_local_server(port=0)
            
            # 保存凭证
            with open(token_file, 'w') as token:
                token.write(creds.to_json())
            print("💾 凭证已保存")
        
        # 测试API调用
        print("📧 测试Gmail API调用...")
        service = build('gmail', 'v1', credentials=creds)
        
        # 获取用户信息
        profile = service.users().getProfile(userId='me').execute()
        email_address = profile.get('emailAddress')
        
        print(f"✅ Gmail API认证成功！")
        print(f"   邮箱地址: {email_address}")
        print(f"   邮件总数: {profile.get('messagesTotal', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Gmail API认证失败: {e}")
        
        if "access_denied" in str(e):
            print("\n🔧 解决方案:")
            print("   1. 访问 Google Cloud Console")
            print("   2. 转到 'API和服务' → 'OAuth同意屏幕'")
            print("   3. 在'测试用户'部分添加: <EMAIL>")
            print("   4. 确保添加了Gmail只读作用域")
        
        return False

def main():
    """主函数"""
    print("🚀 Gmail API快速测试")
    print("=" * 50)
    
    # 1. 检查凭据文件
    if not test_credentials_file():
        return 1
    
    # 2. 测试认证
    if test_simple_auth():
        print("\n🎉 Gmail API配置成功！")
        print("\n📝 下一步:")
        print("   1. 运行完整测试: python test_gmail_api.py")
        print("   2. 启动Gmail监听: python src/gmail_monitor.py")
        return 0
    else:
        print("\n❌ Gmail API配置失败")
        return 1

if __name__ == "__main__":
    main()
