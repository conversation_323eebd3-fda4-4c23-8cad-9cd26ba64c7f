version: '3.8'

services:
  # API服务
  api:
    build: .
    container_name: email_verification_api
    ports:
      - "8000:8000"
    environment:
      - EMAIL_DOMAIN=${EMAIL_DOMAIN:-yourdomain.com}
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - DATABASE_URL=sqlite:///data/email_verification.db
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Gmail监听服务
  gmail_monitor:
    build: .
    container_name: email_verification_monitor
    command: ["python", "src/gmail_monitor.py"]
    environment:
      - EMAIL_DOMAIN=${EMAIL_DOMAIN:-yourdomain.com}
      - DATABASE_URL=sqlite:///data/email_verification.db
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped
    depends_on:
      - api

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: email_verification_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    restart: unless-stopped
    profiles:
      - production

volumes:
  data:
    driver: local
  logs:
    driver: local

networks:
  default:
    name: email_verification_network
