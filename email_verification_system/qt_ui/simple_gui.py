#!/usr/bin/env python3
"""
简化的Qt GUI启动脚本
"""

import sys
import os
from pathlib import Path

# 添加父目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit, QLineEdit, QGridLayout, QGroupBox
    from PyQt5.QtCore import QTimer
    from PyQt5.QtGui import QFont
    import requests
except ImportError as e:
    print(f"导入错误: {e}")
    print("请安装PyQt5: pip install PyQt5")
    sys.exit(1)

class SimpleEmailUI(QMainWindow):
    """简化的邮箱验证码UI"""
    
    def __init__(self):
        super().__init__()
        self.api_base = "http://localhost:8000"
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("邮箱验证码管理系统 - 616866.xyz")
        self.setGeometry(200, 200, 800, 600)
        
        # 中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("📧 邮箱验证码管理系统")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title)
        
        # 状态显示
        self.status_label = QLabel("🔴 检查API连接中...")
        layout.addWidget(self.status_label)
        
        # 生成邮箱组
        generate_group = QGroupBox("生成邮箱")
        generate_layout = QGridLayout()
        
        generate_layout.addWidget(QLabel("应用名称:"), 0, 0)
        self.app_name_input = QLineEdit()
        self.app_name_input.setPlaceholderText("例如: wechat, alipay")
        generate_layout.addWidget(self.app_name_input, 0, 1)
        
        generate_layout.addWidget(QLabel("前缀:"), 1, 0)
        self.prefix_input = QLineEdit()
        self.prefix_input.setPlaceholderText("例如: verify, test")
        generate_layout.addWidget(self.prefix_input, 1, 1)
        
        self.generate_btn = QPushButton("🔄 生成邮箱")
        self.generate_btn.clicked.connect(self.generate_email)
        generate_layout.addWidget(self.generate_btn, 2, 0, 1, 2)
        
        generate_group.setLayout(generate_layout)
        layout.addWidget(generate_group)
        
        # 结果显示
        result_group = QGroupBox("结果")
        result_layout = QVBoxLayout()
        
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(200)
        result_layout.addWidget(self.result_text)
        
        result_group.setLayout(result_layout)
        layout.addWidget(result_group)
        
        # 验证码查询组
        code_group = QGroupBox("验证码查询")
        code_layout = QGridLayout()
        
        code_layout.addWidget(QLabel("邮箱ID:"), 0, 0)
        self.email_id_input = QLineEdit()
        self.email_id_input.setPlaceholderText("输入邮箱ID")
        code_layout.addWidget(self.email_id_input, 0, 1)
        
        self.query_btn = QPushButton("🔍 查询验证码")
        self.query_btn.clicked.connect(self.query_code)
        code_layout.addWidget(self.query_btn, 1, 0, 1, 2)
        
        code_group.setLayout(code_layout)
        layout.addWidget(code_group)
        
        # 验证码显示
        self.code_text = QTextEdit()
        self.code_text.setMaximumHeight(150)
        layout.addWidget(self.code_text)
        
        central_widget.setLayout(layout)
        
        # 定时器检查API状态
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_api_status)
        self.timer.start(5000)  # 每5秒检查一次
        
        # 初始检查
        self.check_api_status()
        
    def check_api_status(self):
        """检查API状态"""
        try:
            response = requests.get(f"{self.api_base}/health", timeout=2)
            if response.status_code == 200:
                self.status_label.setText("🟢 API服务器: 已连接")
                self.status_label.setStyleSheet("color: green;")
            else:
                self.status_label.setText("🔴 API服务器: 响应异常")
                self.status_label.setStyleSheet("color: red;")
        except:
            self.status_label.setText("🔴 API服务器: 未连接")
            self.status_label.setStyleSheet("color: red;")
            
    def generate_email(self):
        """生成邮箱"""
        app_name = self.app_name_input.text().strip()
        if not app_name:
            self.result_text.setText("❌ 请输入应用名称")
            return
            
        data = {"app_name": app_name}
        if self.prefix_input.text().strip():
            data["prefix"] = self.prefix_input.text().strip()
            
        try:
            response = requests.post(f"{self.api_base}/api/generate-email", json=data)
            result = response.json()
            
            if result.get("success"):
                self.result_text.setText(f"""✅ 邮箱生成成功！

邮箱地址: {result.get('email_address')}
邮箱ID: {result.get('email_id')}
应用名称: {result.get('app_name')}
创建时间: {result.get('created_at')}

请将此邮箱地址用于接收验证码。""")
                
                # 自动填入邮箱ID
                self.email_id_input.setText(str(result.get('email_id', '')))
                
            else:
                self.result_text.setText(f"❌ 生成失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            self.result_text.setText(f"❌ 请求失败: {str(e)}")
            
    def query_code(self):
        """查询验证码"""
        email_id = self.email_id_input.text().strip()
        if not email_id:
            self.code_text.setText("❌ 请输入邮箱ID")
            return
            
        try:
            response = requests.get(f"{self.api_base}/api/get-code/{email_id}")
            result = response.json()
            
            if result.get("success"):
                self.code_text.setText(f"""✅ 验证码获取成功！

验证码: {result.get('code')}
发件人: {result.get('sender_email', '未知')}
邮件主题: {result.get('subject', '无主题')}
提取时间: {result.get('extracted_at', '未知')}
使用状态: {'已使用' if result.get('is_used') else '未使用'}""")
            else:
                self.code_text.setText(f"ℹ️ {result.get('message', '暂无验证码')}")
                
        except Exception as e:
            self.code_text.setText(f"❌ 查询失败: {str(e)}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("邮箱验证码管理系统")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    window = SimpleEmailUI()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
