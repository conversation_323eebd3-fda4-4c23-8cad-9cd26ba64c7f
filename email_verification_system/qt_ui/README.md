# Qt GUI 用户界面

## 概述

这是邮箱验证码管理系统的Qt图形用户界面，提供了直观的操作界面来管理临时邮箱和验证码。

## 功能特性

### 🎯 主要功能

1. **邮箱生成**
   - 输入应用名称和可选前缀
   - 自动生成唯一的临时邮箱地址
   - 显示邮箱ID用于后续查询

2. **验证码查询**
   - 通过邮箱ID查询最新验证码
   - 显示验证码详细信息
   - 实时状态更新

3. **系统监控**
   - API服务器连接状态
   - 自动状态检查
   - 错误提示和处理

## 文件说明

### 核心文件

- `simple_gui.py` - 简化版GUI应用（推荐使用）
- `main.py` - 完整版GUI应用主程序
- `email_verification_ui.py` - 完整版UI组件
- `start_gui.py` - GUI启动脚本
- `styles.qss` - Qt样式表

### 配置文件

- `requirements.txt` - Python依赖包

## 安装和启动

### 1. 安装依赖

```bash
cd qt_ui
pip install -r requirements.txt
```

### 2. 启动方式

#### 方式1: 简化版GUI（推荐）
```bash
python simple_gui.py
```

#### 方式2: 完整版GUI
```bash
python start_gui.py
```

#### 方式3: 直接启动
```bash
python main.py
```

## 使用指南

### 📧 生成邮箱

1. **输入应用名称**
   - 在"应用名称"字段输入应用标识
   - 例如: `wechat`, `alipay`, `taobao`

2. **输入前缀（可选）**
   - 在"前缀"字段输入自定义前缀
   - 例如: `verify`, `test`, `demo`

3. **点击"生成邮箱"按钮**
   - 系统会自动生成唯一邮箱地址
   - 显示邮箱ID和详细信息

### 🔍 查询验证码

1. **输入邮箱ID**
   - 在"邮箱ID"字段输入要查询的邮箱ID
   - 可以从邮箱生成结果中获取

2. **点击"查询验证码"按钮**
   - 系统会查询最新的验证码
   - 显示验证码和相关信息

### 📊 状态监控

- **绿色圆点** 🟢 - API服务器已连接
- **红色圆点** 🔴 - API服务器未连接
- 系统每5秒自动检查一次连接状态

## 界面截图说明

### 主界面布局

```
┌─────────────────────────────────────┐
│ 📧 邮箱验证码管理系统                │
├─────────────────────────────────────┤
│ 🟢 API服务器: 已连接                │
├─────────────────────────────────────┤
│ ┌─ 生成邮箱 ─────────────────────┐  │
│ │ 应用名称: [________________]   │  │
│ │ 前缀:     [________________]   │  │
│ │ [🔄 生成邮箱]                  │  │
│ └───────────────────────────────────┘  │
├─────────────────────────────────────┤
│ ┌─ 结果 ─────────────────────────┐  │
│ │ ✅ 邮箱生成成功！              │  │
│ │ 邮箱地址: verify_wechat_...    │  │
│ │ 邮箱ID: 123                    │  │
│ └───────────────────────────────────┘  │
├─────────────────────────────────────┤
│ ┌─ 验证码查询 ───────────────────┐  │
│ │ 邮箱ID: [________________]     │  │
│ │ [🔍 查询验证码]                │  │
│ └───────────────────────────────────┘  │
├─────────────────────────────────────┤
│ ┌─ 验证码信息 ───────────────────┐  │
│ │ ✅ 验证码获取成功！            │  │
│ │ 验证码: 123456                 │  │
│ │ 发件人: <EMAIL>     │  │
│ └───────────────────────────────────┘  │
└─────────────────────────────────────┘
```

## 常见问题

### Q: GUI无法启动
**A**: 检查以下项目：
- 确保已安装PyQt5: `pip install PyQt5`
- 检查Python版本（需要3.6+）
- 查看错误信息

### Q: 显示"API服务器未连接"
**A**: 解决方案：
- 确保API服务器正在运行
- 启动命令: `python src/api_server.py`
- 检查端口8000是否被占用

### Q: 邮箱生成失败
**A**: 可能原因：
- API服务器未运行
- 网络连接问题
- 输入参数错误

### Q: 查询不到验证码
**A**: 检查项目：
- 邮箱ID是否正确
- 是否已发送邮件到生成的邮箱
- Gmail监听服务是否运行
- 域名邮箱转发是否配置

## 技术细节

### 依赖库

- **PyQt5** - GUI框架
- **requests** - HTTP请求库
- **python-dateutil** - 日期处理

### API接口

GUI通过以下API与后端通信：

- `GET /health` - 健康检查
- `POST /api/generate-email` - 生成邮箱
- `GET /api/get-code/{email_id}` - 查询验证码

### 错误处理

- 网络连接错误自动重试
- 用户输入验证
- 友好的错误提示信息

## 开发说明

### 扩展功能

如需添加新功能，可以：

1. 修改 `simple_gui.py` 添加新的UI组件
2. 在相应的事件处理函数中添加逻辑
3. 更新样式表 `styles.qss`

### 样式定制

编辑 `styles.qss` 文件可以自定义界面样式：

- 颜色主题
- 字体大小
- 按钮样式
- 布局间距

## 支持

如果遇到问题：

1. 查看控制台错误信息
2. 检查API服务器日志
3. 确认系统配置正确
4. 参考主项目README.md
