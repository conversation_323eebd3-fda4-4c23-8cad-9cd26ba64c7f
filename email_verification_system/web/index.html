<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱验证码管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .code-display {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
            margin: 15px 0;
            letter-spacing: 3px;
        }
        
        .email-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .email-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .email-item:last-child {
            border-bottom: none;
        }
        
        .email-info {
            flex: 1;
        }
        
        .email-address {
            font-weight: bold;
            color: #667eea;
        }
        
        .email-meta {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>邮箱验证码管理系统</h1>
            <p>自动生成临时邮箱并获取验证码 - 域名: 616866.xyz</p>
        </div>
        
        <div class="grid">
            <!-- 生成邮箱 -->
            <div class="card">
                <h2>🔄 生成新邮箱</h2>
                <form id="generateForm">
                    <div class="form-group">
                        <label for="appName">应用名称 *</label>
                        <input type="text" id="appName" name="appName" required placeholder="例如: wechat, alipay">
                    </div>
                    <div class="form-group">
                        <label for="prefix">自定义前缀</label>
                        <input type="text" id="prefix" name="prefix" placeholder="例如: test, demo">
                    </div>
                    <div class="form-group">
                        <label for="customSuffix">自定义后缀</label>
                        <input type="text" id="customSuffix" name="customSuffix" placeholder="例如: prod, dev">
                    </div>
                    <button type="submit" class="btn">生成邮箱</button>
                </form>
                <div id="generateResult" class="result"></div>
            </div>
            
            <!-- 获取验证码 -->
            <div class="card">
                <h2>📧 获取验证码</h2>
                <form id="getCodeForm">
                    <div class="form-group">
                        <label for="emailId">邮箱ID *</label>
                        <input type="number" id="emailId" name="emailId" required placeholder="输入邮箱ID">
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="includeUsed" name="includeUsed">
                            包含已使用的验证码
                        </label>
                    </div>
                    <button type="submit" class="btn">获取验证码</button>
                </form>
                <div id="codeResult" class="result"></div>
            </div>
        </div>
        
        <!-- 邮箱列表 -->
        <div class="card">
            <h2>📋 邮箱列表</h2>
            <div class="form-group">
                <label for="filterApp">按应用过滤</label>
                <select id="filterApp">
                    <option value="">所有应用</option>
                </select>
            </div>
            <button onclick="loadEmails()" class="btn">刷新列表</button>
            <div id="emailList" class="email-list">
                <div class="loading">加载中...</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        
        // 生成邮箱
        document.getElementById('generateForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = {
                app_name: formData.get('appName'),
                prefix: formData.get('prefix') || null,
                custom_suffix: formData.get('customSuffix') || null
            };
            
            try {
                const response = await fetch(`${API_BASE}/generate-email`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                const resultDiv = document.getElementById('generateResult');
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>邮箱生成成功！</strong><br>
                        邮箱地址: <strong>${result.email_address}</strong><br>
                        邮箱ID: <strong>${result.email_id}</strong><br>
                        创建时间: ${new Date(result.created_at).toLocaleString()}
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>生成失败:</strong> ${result.message}`;
                }
                
                resultDiv.style.display = 'block';
                loadEmails(); // 刷新邮箱列表
                
            } catch (error) {
                const resultDiv = document.getElementById('generateResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>请求失败:</strong> ${error.message}`;
                resultDiv.style.display = 'block';
            }
        });
        
        // 获取验证码
        document.getElementById('getCodeForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const emailId = formData.get('emailId');
            const includeUsed = formData.get('includeUsed') === 'on';
            
            try {
                const response = await fetch(`${API_BASE}/get-code/${emailId}?include_used=${includeUsed}`);
                const result = await response.json();
                const resultDiv = document.getElementById('codeResult');
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>验证码获取成功！</strong>
                        <div class="code-display">${result.code}</div>
                        发件人: ${result.sender_email || '未知'}<br>
                        主题: ${result.subject || '无主题'}<br>
                        提取时间: ${new Date(result.extracted_at).toLocaleString()}<br>
                        过期时间: ${new Date(result.expires_at).toLocaleString()}
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>获取失败:</strong> ${result.message}`;
                }
                
                resultDiv.style.display = 'block';
                
            } catch (error) {
                const resultDiv = document.getElementById('codeResult');
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>请求失败:</strong> ${error.message}`;
                resultDiv.style.display = 'block';
            }
        });
        
        // 加载邮箱列表
        async function loadEmails() {
            const listDiv = document.getElementById('emailList');
            listDiv.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/emails`);
                const result = await response.json();
                
                if (result.success) {
                    const emails = result.emails;
                    
                    if (emails.length === 0) {
                        listDiv.innerHTML = '<div class="loading">暂无邮箱记录</div>';
                        return;
                    }
                    
                    listDiv.innerHTML = emails.map(email => `
                        <div class="email-item">
                            <div class="email-info">
                                <div class="email-address">${email.email_address}</div>
                                <div class="email-meta">
                                    应用: ${email.app_name} | 
                                    ID: ${email.id} | 
                                    创建: ${new Date(email.created_at).toLocaleString()}
                                </div>
                            </div>
                            <span class="status-badge ${email.is_active ? 'status-active' : 'status-inactive'}">
                                ${email.is_active ? '活跃' : '停用'}
                            </span>
                        </div>
                    `).join('');
                    
                    // 更新应用过滤器
                    updateAppFilter(emails);
                    
                } else {
                    listDiv.innerHTML = '<div class="loading">加载失败</div>';
                }
                
            } catch (error) {
                listDiv.innerHTML = '<div class="loading">加载失败</div>';
            }
        }
        
        // 更新应用过滤器
        function updateAppFilter(emails) {
            const filterSelect = document.getElementById('filterApp');
            const apps = [...new Set(emails.map(email => email.app_name))];
            
            filterSelect.innerHTML = '<option value="">所有应用</option>' +
                apps.map(app => `<option value="${app}">${app}</option>`).join('');
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            loadEmails();
        });
    </script>
</body>
</html>
