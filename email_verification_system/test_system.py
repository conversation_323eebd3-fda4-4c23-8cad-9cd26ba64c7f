#!/usr/bin/env python3
"""
系统功能测试脚本
"""

import requests
import json
import time
import sys

API_BASE = "http://localhost:8000"

def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{API_BASE}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过: {data}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_generate_email():
    """测试生成邮箱"""
    print("\n📧 测试生成邮箱...")
    try:
        data = {
            "app_name": "test_app",
            "prefix": "demo"
        }
        response = requests.post(f"{API_BASE}/api/generate-email", json=data)
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ 邮箱生成成功:")
                print(f"   邮箱地址: {result.get('email_address')}")
                print(f"   邮箱ID: {result.get('email_id')}")
                print(f"   应用名称: {result.get('app_name')}")
                return result.get('email_id')
            else:
                print(f"❌ 邮箱生成失败: {result.get('message')}")
                return None
        else:
            print(f"❌ 邮箱生成请求失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 邮箱生成异常: {e}")
        return None

def test_get_code(email_id):
    """测试获取验证码"""
    print(f"\n🔐 测试获取验证码 (邮箱ID: {email_id})...")
    try:
        response = requests.get(f"{API_BASE}/api/get-code/{email_id}")
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ 验证码获取成功: {result.get('code')}")
                return True
            else:
                print(f"ℹ️ 暂无验证码: {result.get('message')}")
                return True  # 这是正常的，因为还没有收到邮件
        else:
            print(f"❌ 验证码获取请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 验证码获取异常: {e}")
        return False

def test_list_emails():
    """测试列出邮箱"""
    print("\n📋 测试列出邮箱...")
    try:
        response = requests.get(f"{API_BASE}/api/emails")
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                emails = result.get("emails", [])
                print(f"✅ 邮箱列表获取成功，共 {len(emails)} 个邮箱:")
                for email in emails[:3]:  # 只显示前3个
                    print(f"   - {email.get('email_address')} (ID: {email.get('id')}, 应用: {email.get('app_name')})")
                return True
            else:
                print(f"❌ 邮箱列表获取失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 邮箱列表请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 邮箱列表异常: {e}")
        return False

def test_api_docs():
    """测试API文档"""
    print("\n📚 测试API文档...")
    try:
        response = requests.get(f"{API_BASE}/docs")
        if response.status_code == 200:
            print("✅ API文档可访问")
            print(f"   访问地址: {API_BASE}/docs")
            return True
        else:
            print(f"❌ API文档访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API文档访问异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始系统功能测试...\n")
    
    # 测试计数
    total_tests = 0
    passed_tests = 0
    
    # 1. 健康检查
    total_tests += 1
    if test_health_check():
        passed_tests += 1
    
    # 2. 生成邮箱
    total_tests += 1
    email_id = test_generate_email()
    if email_id:
        passed_tests += 1
    
    # 3. 获取验证码
    if email_id:
        total_tests += 1
        if test_get_code(email_id):
            passed_tests += 1
    
    # 4. 列出邮箱
    total_tests += 1
    if test_list_emails():
        passed_tests += 1
    
    # 5. API文档
    total_tests += 1
    if test_api_docs():
        passed_tests += 1
    
    # 测试结果
    print(f"\n📊 测试结果:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   失败测试: {total_tests - passed_tests}")
    print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！系统运行正常。")
        return 0
    else:
        print(f"\n⚠️ 有 {total_tests - passed_tests} 个测试失败，请检查系统配置。")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 测试过程中发生异常: {e}")
        sys.exit(1)
