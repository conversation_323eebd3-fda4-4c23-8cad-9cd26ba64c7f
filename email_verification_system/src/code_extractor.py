"""
验证码提取器模块
"""

import re
from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict, Any, Tuple
from bs4 import BeautifulSoup
from loguru import logger
from sqlalchemy.orm import Session

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import CODE_EXTRACTION_CONFIG
from src.models.verification_code import VerificationCode
from src.models.email_record import EmailRecord

class CodeExtractor:
    """验证码提取器"""
    
    def __init__(self):
        self.patterns = CODE_EXTRACTION_CONFIG["patterns"]
        self.timeout = CODE_EXTRACTION_CONFIG["timeout"]
        self.max_attempts = CODE_EXTRACTION_CONFIG["max_attempts"]
    
    def extract_code_from_email(
        self, 
        email_content: str, 
        email_subject: str = "",
        sender_email: str = "",
        email_record_id: Optional[int] = None,
        db: Optional[Session] = None
    ) -> Dict[str, Any]:
        """
        从邮件内容中提取验证码
        
        Args:
            email_content: 邮件内容
            email_subject: 邮件主题
            sender_email: 发件人邮箱
            email_record_id: 邮箱记录ID
            db: 数据库会话
            
        Returns:
            提取结果字典
        """
        try:
            # 清理邮件内容
            clean_content = self._clean_email_content(email_content)
            
            # 合并主题和内容进行提取
            full_text = f"{email_subject} {clean_content}"
            
            # 尝试提取验证码
            extracted_codes = self._extract_codes(full_text)
            
            if not extracted_codes:
                logger.warning(f"未能从邮件中提取到验证码: {sender_email}")
                return {
                    "success": False,
                    "message": "未找到验证码",
                    "sender_email": sender_email,
                    "subject": email_subject
                }
            
            # 选择最可能的验证码
            best_code = self._select_best_code(extracted_codes, full_text)
            
            # 计算过期时间
            expires_at = datetime.now(timezone.utc) + timedelta(seconds=self.timeout)
            
            # 保存验证码记录
            verification_code = None
            if db and email_record_id:
                verification_code = VerificationCode(
                    email_record_id=email_record_id,
                    code=best_code,
                    sender_email=sender_email,
                    subject=email_subject,
                    email_content=email_content[:1000],  # 限制长度
                    expires_at=expires_at
                )
                
                db.add(verification_code)
                db.commit()
                db.refresh(verification_code)
                
                logger.info(f"成功提取验证码: {best_code} from {sender_email}")
            
            return {
                "success": True,
                "code": best_code,
                "code_id": verification_code.id if verification_code else None,
                "sender_email": sender_email,
                "subject": email_subject,
                "extracted_at": datetime.now(timezone.utc).isoformat(),
                "expires_at": expires_at.isoformat(),
                "all_codes": extracted_codes,
                "message": "验证码提取成功"
            }
            
        except Exception as e:
            logger.error(f"提取验证码失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "验证码提取失败"
            }
    
    def _clean_email_content(self, content: str) -> str:
        """清理邮件内容"""
        try:
            # 尝试解析HTML
            soup = BeautifulSoup(content, 'html.parser')
            
            # 移除脚本和样式标签
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 获取纯文本
            text = soup.get_text()
            
            # 清理空白字符
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except Exception:
            # 如果HTML解析失败，直接返回原文本
            return content
    
    def _extract_codes(self, text: str) -> List[str]:
        """使用多个模式提取验证码"""
        codes = []
        
        for pattern in self.patterns:
            try:
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        # 如果是分组匹配，取第一个分组
                        code = match[0] if match else ""
                    else:
                        code = match
                    
                    if code and self._validate_code(code):
                        codes.append(code)
                        
            except Exception as e:
                logger.warning(f"正则表达式匹配失败: {pattern}, 错误: {str(e)}")
                continue
        
        # 去重并保持顺序
        unique_codes = []
        seen = set()
        for code in codes:
            if code not in seen:
                unique_codes.append(code)
                seen.add(code)
        
        return unique_codes
    
    def _validate_code(self, code: str) -> bool:
        """验证验证码格式"""
        if not code:
            return False
        
        # 长度检查
        if len(code) < 4 or len(code) > 8:
            return False
        
        # 检查是否全是数字或字母数字组合
        if not (code.isdigit() or code.isalnum()):
            return False
        
        # 排除明显不是验证码的数字（如年份、电话号码等）
        if code.isdigit():
            num = int(code)
            # 排除年份
            if 1900 <= num <= 2100:
                return False
            # 排除太小的数字
            if num < 1000:
                return False
        
        return True
    
    def _select_best_code(self, codes: List[str], context: str) -> str:
        """从多个验证码中选择最可能的一个"""
        if not codes:
            return ""
        
        if len(codes) == 1:
            return codes[0]
        
        # 评分系统
        scored_codes = []
        
        for code in codes:
            score = 0
            
            # 长度评分（6位数字最常见）
            if len(code) == 6 and code.isdigit():
                score += 10
            elif len(code) == 4 and code.isdigit():
                score += 8
            elif len(code) == 8 and code.isdigit():
                score += 6
            
            # 上下文评分
            code_context = self._get_code_context(code, context)
            if any(keyword in code_context.lower() for keyword in 
                   ["验证码", "verification", "code", "verify"]):
                score += 5
            
            # 位置评分（靠前的验证码通常更重要）
            position = context.find(code)
            if position != -1:
                # 位置越靠前分数越高
                score += max(0, 5 - position // 100)
            
            scored_codes.append((code, score))
        
        # 按分数排序，返回最高分的验证码
        scored_codes.sort(key=lambda x: x[1], reverse=True)
        return scored_codes[0][0]
    
    def _get_code_context(self, code: str, text: str, window: int = 50) -> str:
        """获取验证码周围的上下文"""
        position = text.find(code)
        if position == -1:
            return ""
        
        start = max(0, position - window)
        end = min(len(text), position + len(code) + window)
        
        return text[start:end]
    
    def get_latest_code(
        self, 
        email_record_id: int, 
        db: Session,
        include_used: bool = False
    ) -> Optional[Dict[str, Any]]:
        """获取最新的验证码"""
        try:
            query = db.query(VerificationCode).filter(
                VerificationCode.email_record_id == email_record_id
            )
            
            if not include_used:
                query = query.filter(VerificationCode.is_used == False)
            
            # 只返回未过期的验证码
            now = datetime.now(timezone.utc)
            query = query.filter(
                (VerificationCode.expires_at.is_(None)) |
                (VerificationCode.expires_at > now)
            )
            
            code_record = query.order_by(
                VerificationCode.extracted_at.desc()
            ).first()
            
            if not code_record:
                return None
            
            return code_record.to_dict()
            
        except Exception as e:
            logger.error(f"获取最新验证码失败: {str(e)}")
            return None
    
    def mark_code_used(self, code_id: int, db: Session) -> bool:
        """标记验证码为已使用"""
        try:
            code_record = db.query(VerificationCode).filter(
                VerificationCode.id == code_id
            ).first()
            
            if not code_record:
                return False
            
            code_record.is_used = True
            code_record.used_at = datetime.now(timezone.utc)
            db.commit()
            
            logger.info(f"验证码已标记为使用: {code_record.code}")
            return True
            
        except Exception as e:
            logger.error(f"标记验证码使用失败: {str(e)}")
            return False
