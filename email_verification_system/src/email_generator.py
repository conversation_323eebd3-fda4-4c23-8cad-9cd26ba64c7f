"""
邮箱生成器模块
"""

import re
import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from loguru import logger

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import EMAIL_CONFIG
from src.models.base import get_db
from src.models.email_record import EmailRecord

class EmailGenerator:
    """邮箱地址生成器"""
    
    def __init__(self):
        self.domain = EMAIL_CONFIG["domain"]
        self.default_prefix = EMAIL_CONFIG["default_prefix"]
        self.separator = EMAIL_CONFIG["separator"]
        self.timestamp_format = EMAIL_CONFIG["timestamp_format"]
        self.max_length = EMAIL_CONFIG["max_length"]
    
    def generate_email(
        self, 
        app_name: str, 
        prefix: Optional[str] = None,
        custom_suffix: Optional[str] = None,
        db: Optional[Session] = None
    ) -> Dict[str, Any]:
        """
        生成唯一的邮箱地址
        
        Args:
            app_name: 应用名称
            prefix: 自定义前缀
            custom_suffix: 自定义后缀
            db: 数据库会话
            
        Returns:
            包含邮箱信息的字典
        """
        try:
            # 清理应用名称
            clean_app_name = self._clean_string(app_name)
            
            # 使用提供的前缀或默认前缀
            email_prefix = prefix or self.default_prefix
            clean_prefix = self._clean_string(email_prefix)
            
            # 生成时间戳
            timestamp = datetime.now().strftime(self.timestamp_format)
            
            # 生成唯一标识符
            unique_id = str(uuid.uuid4())[:8]
            
            # 构建邮箱地址
            if custom_suffix:
                clean_suffix = self._clean_string(custom_suffix)
                local_part = f"{clean_prefix}{self.separator}{clean_app_name}{self.separator}{clean_suffix}{self.separator}{unique_id}"
            else:
                local_part = f"{clean_prefix}{self.separator}{clean_app_name}{self.separator}{timestamp}{self.separator}{unique_id}"
            
            # 确保邮箱地址不超过最大长度
            max_local_length = self.max_length - len(self.domain) - 1  # -1 for @
            if len(local_part) > max_local_length:
                # 截断并添加哈希
                hash_suffix = str(hash(local_part))[-6:]
                local_part = local_part[:max_local_length-7] + hash_suffix
            
            email_address = f"{local_part}@{self.domain}"
            
            # 验证邮箱格式
            if not self._validate_email(email_address):
                raise ValueError(f"生成的邮箱地址格式无效: {email_address}")
            
            # 检查邮箱是否已存在
            if db:
                existing = db.query(EmailRecord).filter(
                    EmailRecord.email_address == email_address
                ).first()
                
                if existing:
                    logger.warning(f"邮箱地址已存在: {email_address}")
                    # 重新生成
                    return self.generate_email(app_name, prefix, custom_suffix, db)
            
            # 创建邮箱记录
            email_record = EmailRecord(
                email_address=email_address,
                app_name=app_name,
                prefix=prefix,
                description=f"为应用 {app_name} 生成的临时邮箱"
            )
            
            if db:
                db.add(email_record)
                db.commit()
                db.refresh(email_record)
                logger.info(f"成功生成邮箱: {email_address} for {app_name}")
            
            return {
                "success": True,
                "email_address": email_address,
                "email_id": email_record.id if db else None,
                "app_name": app_name,
                "prefix": prefix,
                "created_at": email_record.created_at.isoformat() if email_record.created_at else None,
                "message": "邮箱地址生成成功"
            }
            
        except Exception as e:
            logger.error(f"生成邮箱地址失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "邮箱地址生成失败"
            }
    
    def _clean_string(self, text: str) -> str:
        """清理字符串，移除特殊字符"""
        # 只保留字母、数字和连字符
        cleaned = re.sub(r'[^a-zA-Z0-9\-]', '', text.lower())
        # 移除连续的连字符
        cleaned = re.sub(r'-+', '-', cleaned)
        # 移除开头和结尾的连字符
        cleaned = cleaned.strip('-')
        return cleaned
    
    def _validate_email(self, email: str) -> bool:
        """验证邮箱地址格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    def get_email_info(self, email_id: int, db: Session) -> Optional[Dict[str, Any]]:
        """获取邮箱信息"""
        try:
            email_record = db.query(EmailRecord).filter(
                EmailRecord.id == email_id
            ).first()
            
            if not email_record:
                return None
            
            return email_record.to_dict()
            
        except Exception as e:
            logger.error(f"获取邮箱信息失败: {str(e)}")
            return None
    
    def deactivate_email(self, email_id: int, db: Session) -> bool:
        """停用邮箱"""
        try:
            email_record = db.query(EmailRecord).filter(
                EmailRecord.id == email_id
            ).first()
            
            if not email_record:
                return False
            
            email_record.is_active = False
            db.commit()
            
            logger.info(f"邮箱已停用: {email_record.email_address}")
            return True
            
        except Exception as e:
            logger.error(f"停用邮箱失败: {str(e)}")
            return False
    
    def list_emails(
        self, 
        app_name: Optional[str] = None,
        is_active: Optional[bool] = None,
        limit: int = 100,
        offset: int = 0,
        db: Session = None
    ) -> Dict[str, Any]:
        """列出邮箱记录"""
        try:
            query = db.query(EmailRecord)
            
            if app_name:
                query = query.filter(EmailRecord.app_name == app_name)
            
            if is_active is not None:
                query = query.filter(EmailRecord.is_active == is_active)
            
            total = query.count()
            emails = query.offset(offset).limit(limit).all()
            
            return {
                "success": True,
                "total": total,
                "emails": [email.to_dict() for email in emails],
                "limit": limit,
                "offset": offset
            }
            
        except Exception as e:
            logger.error(f"列出邮箱记录失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "获取邮箱列表失败"
            }
