"""
数据库操作模块
"""

from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from loguru import logger

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from src.models.base import SessionLocal, create_tables, drop_tables
from src.models.email_record import EmailRecord
from src.models.verification_code import VerificationCode

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.session_factory = SessionLocal
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.session_factory()
    
    def init_database(self):
        """初始化数据库"""
        try:
            create_tables()
            logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise
    
    def reset_database(self):
        """重置数据库（删除所有表并重新创建）"""
        try:
            drop_tables()
            create_tables()
            logger.info("数据库重置成功")
        except Exception as e:
            logger.error(f"数据库重置失败: {str(e)}")
            raise
    
    # 邮箱记录相关操作
    def create_email_record(
        self, 
        email_address: str, 
        app_name: str, 
        prefix: Optional[str] = None,
        description: Optional[str] = None
    ) -> Optional[EmailRecord]:
        """创建邮箱记录"""
        db = self.get_session()
        try:
            # 检查邮箱是否已存在
            existing = db.query(EmailRecord).filter(
                EmailRecord.email_address == email_address
            ).first()
            
            if existing:
                logger.warning(f"邮箱地址已存在: {email_address}")
                return existing
            
            email_record = EmailRecord(
                email_address=email_address,
                app_name=app_name,
                prefix=prefix,
                description=description
            )
            
            db.add(email_record)
            db.commit()
            db.refresh(email_record)
            
            logger.info(f"创建邮箱记录成功: {email_address}")
            return email_record
            
        except Exception as e:
            db.rollback()
            logger.error(f"创建邮箱记录失败: {str(e)}")
            return None
        finally:
            db.close()
    
    def get_email_record(self, email_id: int) -> Optional[EmailRecord]:
        """获取邮箱记录"""
        db = self.get_session()
        try:
            return db.query(EmailRecord).filter(EmailRecord.id == email_id).first()
        except Exception as e:
            logger.error(f"获取邮箱记录失败: {str(e)}")
            return None
        finally:
            db.close()
    
    def get_email_record_by_address(self, email_address: str) -> Optional[EmailRecord]:
        """根据邮箱地址获取记录"""
        db = self.get_session()
        try:
            return db.query(EmailRecord).filter(
                EmailRecord.email_address == email_address
            ).first()
        except Exception as e:
            logger.error(f"根据地址获取邮箱记录失败: {str(e)}")
            return None
        finally:
            db.close()
    
    def list_email_records(
        self,
        app_name: Optional[str] = None,
        is_active: Optional[bool] = None,
        limit: int = 100,
        offset: int = 0
    ) -> Dict[str, Any]:
        """列出邮箱记录"""
        db = self.get_session()
        try:
            query = db.query(EmailRecord)
            
            if app_name:
                query = query.filter(EmailRecord.app_name == app_name)
            
            if is_active is not None:
                query = query.filter(EmailRecord.is_active == is_active)
            
            total = query.count()
            records = query.order_by(desc(EmailRecord.created_at)).offset(offset).limit(limit).all()
            
            return {
                "total": total,
                "records": [record.to_dict() for record in records],
                "limit": limit,
                "offset": offset
            }
            
        except Exception as e:
            logger.error(f"列出邮箱记录失败: {str(e)}")
            return {"total": 0, "records": [], "limit": limit, "offset": offset}
        finally:
            db.close()
    
    def update_email_record(
        self, 
        email_id: int, 
        **kwargs
    ) -> bool:
        """更新邮箱记录"""
        db = self.get_session()
        try:
            record = db.query(EmailRecord).filter(EmailRecord.id == email_id).first()
            if not record:
                return False
            
            for key, value in kwargs.items():
                if hasattr(record, key):
                    setattr(record, key, value)
            
            record.updated_at = datetime.now(timezone.utc)
            db.commit()
            
            logger.info(f"更新邮箱记录成功: {email_id}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"更新邮箱记录失败: {str(e)}")
            return False
        finally:
            db.close()
    
    def deactivate_email_record(self, email_id: int) -> bool:
        """停用邮箱记录"""
        return self.update_email_record(email_id, is_active=False)
    
    # 验证码记录相关操作
    def create_verification_code(
        self,
        email_record_id: int,
        code: str,
        sender_email: Optional[str] = None,
        subject: Optional[str] = None,
        email_content: Optional[str] = None,
        expires_at: Optional[datetime] = None
    ) -> Optional[VerificationCode]:
        """创建验证码记录"""
        db = self.get_session()
        try:
            verification_code = VerificationCode(
                email_record_id=email_record_id,
                code=code,
                sender_email=sender_email,
                subject=subject,
                email_content=email_content,
                expires_at=expires_at
            )
            
            db.add(verification_code)
            db.commit()
            db.refresh(verification_code)
            
            logger.info(f"创建验证码记录成功: {code}")
            return verification_code
            
        except Exception as e:
            db.rollback()
            logger.error(f"创建验证码记录失败: {str(e)}")
            return None
        finally:
            db.close()
    
    def get_latest_verification_code(
        self,
        email_record_id: int,
        include_used: bool = False,
        include_expired: bool = False
    ) -> Optional[VerificationCode]:
        """获取最新的验证码"""
        db = self.get_session()
        try:
            query = db.query(VerificationCode).filter(
                VerificationCode.email_record_id == email_record_id
            )
            
            if not include_used:
                query = query.filter(VerificationCode.is_used == False)
            
            if not include_expired:
                now = datetime.now(timezone.utc)
                query = query.filter(
                    or_(
                        VerificationCode.expires_at.is_(None),
                        VerificationCode.expires_at > now
                    )
                )
            
            return query.order_by(desc(VerificationCode.extracted_at)).first()
            
        except Exception as e:
            logger.error(f"获取最新验证码失败: {str(e)}")
            return None
        finally:
            db.close()
    
    def mark_verification_code_used(self, code_id: int) -> bool:
        """标记验证码为已使用"""
        db = self.get_session()
        try:
            code = db.query(VerificationCode).filter(VerificationCode.id == code_id).first()
            if not code:
                return False
            
            code.is_used = True
            code.used_at = datetime.now(timezone.utc)
            db.commit()
            
            logger.info(f"验证码已标记为使用: {code.code}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"标记验证码使用失败: {str(e)}")
            return False
        finally:
            db.close()
    
    def list_verification_codes(
        self,
        email_record_id: Optional[int] = None,
        is_used: Optional[bool] = None,
        limit: int = 100,
        offset: int = 0
    ) -> Dict[str, Any]:
        """列出验证码记录"""
        db = self.get_session()
        try:
            query = db.query(VerificationCode)
            
            if email_record_id:
                query = query.filter(VerificationCode.email_record_id == email_record_id)
            
            if is_used is not None:
                query = query.filter(VerificationCode.is_used == is_used)
            
            total = query.count()
            codes = query.order_by(desc(VerificationCode.extracted_at)).offset(offset).limit(limit).all()
            
            return {
                "total": total,
                "codes": [code.to_dict() for code in codes],
                "limit": limit,
                "offset": offset
            }
            
        except Exception as e:
            logger.error(f"列出验证码记录失败: {str(e)}")
            return {"total": 0, "codes": [], "limit": limit, "offset": offset}
        finally:
            db.close()
    
    # 统计相关操作
    def get_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        db = self.get_session()
        try:
            # 邮箱统计
            total_emails = db.query(EmailRecord).count()
            active_emails = db.query(EmailRecord).filter(EmailRecord.is_active == True).count()
            
            # 验证码统计
            total_codes = db.query(VerificationCode).count()
            used_codes = db.query(VerificationCode).filter(VerificationCode.is_used == True).count()
            
            # 今日统计
            today = datetime.now(timezone.utc).date()
            today_emails = db.query(EmailRecord).filter(
                EmailRecord.created_at >= today
            ).count()
            today_codes = db.query(VerificationCode).filter(
                VerificationCode.extracted_at >= today
            ).count()
            
            return {
                "emails": {
                    "total": total_emails,
                    "active": active_emails,
                    "today": today_emails
                },
                "codes": {
                    "total": total_codes,
                    "used": used_codes,
                    "unused": total_codes - used_codes,
                    "today": today_codes
                }
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}
        finally:
            db.close()

# 全局数据库管理器实例
db_manager = DatabaseManager()
