"""
Gmail监听服务模块
"""

import base64
import time
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from email.mime.text import MIMEText

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from loguru import logger
from sqlalchemy.orm import Session

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import GMAIL_CONFIG, EMAIL_FILTER_CONFIG
from src.models.base import SessionLocal
from src.models.email_record import EmailRecord
from src.code_extractor import CodeExtractor

class GmailMonitor:
    """Gmail邮件监听器"""
    
    def __init__(self):
        self.credentials_file = GMAIL_CONFIG["credentials_file"]
        self.token_file = GMAIL_CONFIG["token_file"]
        self.scopes = GMAIL_CONFIG["scopes"]
        self.check_interval = GMAIL_CONFIG["check_interval"]
        
        self.service = None
        self.code_extractor = CodeExtractor()
        self.last_check_time = None
        
    def authenticate(self) -> bool:
        """认证Gmail API"""
        try:
            creds = None
            
            # 检查是否存在token文件
            if self.token_file.exists():
                creds = Credentials.from_authorized_user_file(str(self.token_file), self.scopes)
            
            # 如果没有有效凭证，进行OAuth流程
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                else:
                    if not self.credentials_file.exists():
                        logger.error(f"Gmail凭证文件不存在: {self.credentials_file}")
                        return False
                    
                    flow = InstalledAppFlow.from_client_secrets_file(
                        str(self.credentials_file), self.scopes
                    )
                    creds = flow.run_local_server(port=0)
                
                # 保存凭证
                with open(self.token_file, 'w') as token:
                    token.write(creds.to_json())
            
            # 构建Gmail服务
            self.service = build('gmail', 'v1', credentials=creds)
            logger.info("Gmail API认证成功")
            return True
            
        except Exception as e:
            logger.error(f"Gmail API认证失败: {str(e)}")
            return False
    
    def get_recent_messages(self, max_results: int = 10) -> List[Dict[str, Any]]:
        """获取最近的邮件"""
        try:
            if not self.service:
                logger.error("Gmail服务未初始化")
                return []
            
            # 构建查询条件
            query = self._build_search_query()
            
            # 获取邮件列表
            results = self.service.users().messages().list(
                userId='me',
                q=query,
                maxResults=max_results
            ).execute()
            
            messages = results.get('messages', [])
            
            detailed_messages = []
            for message in messages:
                detail = self.get_message_detail(message['id'])
                if detail:
                    detailed_messages.append(detail)
            
            return detailed_messages
            
        except HttpError as e:
            logger.error(f"获取Gmail邮件失败: {str(e)}")
            return []
    
    def get_message_detail(self, message_id: str) -> Optional[Dict[str, Any]]:
        """获取邮件详细信息"""
        try:
            message = self.service.users().messages().get(
                userId='me',
                id=message_id,
                format='full'
            ).execute()
            
            # 解析邮件头
            headers = message['payload'].get('headers', [])
            subject = self._get_header_value(headers, 'Subject')
            sender = self._get_header_value(headers, 'From')
            date = self._get_header_value(headers, 'Date')
            to = self._get_header_value(headers, 'To')
            
            # 解析邮件内容
            body = self._extract_message_body(message['payload'])
            
            return {
                'id': message_id,
                'subject': subject,
                'sender': sender,
                'date': date,
                'to': to,
                'body': body,
                'snippet': message.get('snippet', ''),
                'thread_id': message.get('threadId'),
                'label_ids': message.get('labelIds', [])
            }
            
        except HttpError as e:
            logger.error(f"获取邮件详情失败: {message_id}, 错误: {str(e)}")
            return None
    
    def _build_search_query(self) -> str:
        """构建Gmail搜索查询"""
        query_parts = []
        
        # 添加主题关键词
        subject_keywords = EMAIL_FILTER_CONFIG.get("subject_keywords", [])
        if subject_keywords:
            subject_query = " OR ".join([f'subject:"{keyword}"' for keyword in subject_keywords])
            query_parts.append(f"({subject_query})")
        
        # 添加发件人过滤
        allowed_senders = EMAIL_FILTER_CONFIG.get("allowed_senders", [])
        if allowed_senders:
            sender_query = " OR ".join([f'from:"{sender}"' for sender in allowed_senders])
            query_parts.append(f"({sender_query})")
        
        # 添加时间过滤（最近24小时）
        query_parts.append("newer_than:1d")
        
        # 只查看收件箱
        query_parts.append("in:inbox")
        
        return " ".join(query_parts) if query_parts else "in:inbox"
    
    def _get_header_value(self, headers: List[Dict], name: str) -> str:
        """从邮件头中获取指定字段的值"""
        for header in headers:
            if header['name'].lower() == name.lower():
                return header['value']
        return ""
    
    def _extract_message_body(self, payload: Dict) -> str:
        """提取邮件正文"""
        body = ""
        
        if 'parts' in payload:
            # 多部分邮件
            for part in payload['parts']:
                if part['mimeType'] == 'text/plain':
                    data = part['body'].get('data')
                    if data:
                        body += base64.urlsafe_b64decode(data).decode('utf-8')
                elif part['mimeType'] == 'text/html':
                    data = part['body'].get('data')
                    if data:
                        body += base64.urlsafe_b64decode(data).decode('utf-8')
                elif 'parts' in part:
                    # 递归处理嵌套部分
                    body += self._extract_message_body(part)
        else:
            # 单部分邮件
            if payload['mimeType'] in ['text/plain', 'text/html']:
                data = payload['body'].get('data')
                if data:
                    body = base64.urlsafe_b64decode(data).decode('utf-8')
        
        return body
    
    def process_new_messages(self) -> Dict[str, Any]:
        """处理新邮件"""
        try:
            messages = self.get_recent_messages()
            processed_count = 0
            extracted_codes = []
            
            db = SessionLocal()
            
            try:
                for message in messages:
                    # 检查邮件是否包含我们的域名
                    to_email = message.get('to', '')
                    if not self._is_our_domain_email(to_email):
                        continue
                    
                    # 查找对应的邮箱记录
                    email_record = self._find_email_record(to_email, db)
                    if not email_record:
                        logger.warning(f"未找到邮箱记录: {to_email}")
                        continue
                    
                    # 提取验证码
                    result = self.code_extractor.extract_code_from_email(
                        email_content=message.get('body', ''),
                        email_subject=message.get('subject', ''),
                        sender_email=message.get('sender', ''),
                        email_record_id=email_record.id,
                        db=db
                    )
                    
                    if result.get('success'):
                        extracted_codes.append(result)
                        processed_count += 1
                        logger.info(f"处理邮件成功: {message.get('subject')} -> {result.get('code')}")
                
                return {
                    "success": True,
                    "processed_count": processed_count,
                    "extracted_codes": extracted_codes,
                    "total_messages": len(messages),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"处理新邮件失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "processed_count": 0
            }
    
    def _is_our_domain_email(self, email: str) -> bool:
        """检查是否是我们域名的邮箱"""
        from config.settings import EMAIL_CONFIG
        domain = EMAIL_CONFIG["domain"]
        return email.endswith(f"@{domain}")
    
    def _find_email_record(self, email_address: str, db: Session) -> Optional[EmailRecord]:
        """查找邮箱记录"""
        # 提取邮箱地址（可能包含显示名称）
        import re
        email_match = re.search(r'[\w\.-]+@[\w\.-]+\.\w+', email_address)
        if email_match:
            clean_email = email_match.group()
        else:
            clean_email = email_address
        
        return db.query(EmailRecord).filter(
            EmailRecord.email_address == clean_email,
            EmailRecord.is_active == True
        ).first()
    
    def start_monitoring(self):
        """开始监听邮件"""
        logger.info("开始Gmail邮件监听服务")
        
        if not self.authenticate():
            logger.error("Gmail认证失败，无法启动监听服务")
            return
        
        while True:
            try:
                logger.info("检查新邮件...")
                result = self.process_new_messages()
                
                if result.get('success'):
                    logger.info(f"处理完成: {result.get('processed_count')}/{result.get('total_messages')} 邮件")
                else:
                    logger.error(f"处理邮件失败: {result.get('error')}")
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                logger.info("收到停止信号，退出监听服务")
                break
            except Exception as e:
                logger.error(f"监听服务异常: {str(e)}")
                time.sleep(self.check_interval)

if __name__ == "__main__":
    monitor = GmailMonitor()
    monitor.start_monitoring()
