"""
验证码记录模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from .base import Base

class VerificationCode(Base):
    """验证码记录表"""
    __tablename__ = "verification_codes"
    
    id = Column(Integer, primary_key=True, index=True)
    email_record_id = Column(Integer, ForeignKey("email_records.id"), nullable=False)
    code = Column(String(20), nullable=False)
    sender_email = Column(String(255), nullable=True)
    subject = Column(String(500), nullable=True)
    email_content = Column(Text, nullable=True)
    extracted_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True)
    is_used = Column(Boolean, default=False)
    used_at = Column(DateTime(timezone=True), nullable=True)
    
    # 关联关系
    email_record = relationship("EmailRecord", backref="verification_codes")
    
    def __repr__(self):
        return f"<VerificationCode(id={self.id}, code={self.code}, email_id={self.email_record_id})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "email_record_id": self.email_record_id,
            "code": self.code,
            "sender_email": self.sender_email,
            "subject": self.subject,
            "email_content": self.email_content,
            "extracted_at": self.extracted_at.isoformat() if self.extracted_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "is_used": self.is_used,
            "used_at": self.used_at.isoformat() if self.used_at else None,
        }
    
    @property
    def is_expired(self):
        """检查验证码是否已过期"""
        if not self.expires_at:
            return False
        from datetime import datetime, timezone
        return datetime.now(timezone.utc) > self.expires_at
