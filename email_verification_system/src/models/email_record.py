"""
邮箱记录模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text
from sqlalchemy.sql import func
from .base import Base

class EmailRecord(Base):
    """邮箱记录表"""
    __tablename__ = "email_records"
    
    id = Column(Integer, primary_key=True, index=True)
    email_address = Column(String(255), unique=True, index=True, nullable=False)
    app_name = Column(String(100), nullable=False)
    prefix = Column(String(50), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    description = Column(Text, nullable=True)
    
    def __repr__(self):
        return f"<EmailRecord(id={self.id}, email={self.email_address}, app={self.app_name})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "email_address": self.email_address,
            "app_name": self.app_name,
            "prefix": self.prefix,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_active": self.is_active,
            "description": self.description,
        }
