"""
API服务器模块
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, EmailStr
from typing import Optional, List
from sqlalchemy.orm import Session
from loguru import logger

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import API_CONFIG, SECURITY_CONFIG
from src.models.base import get_db, create_tables
from src.email_generator import EmailGenerator
from src.code_extractor import CodeExtractor
from src.gmail_monitor import GmailMonitor

# 创建FastAPI应用
app = FastAPI(
    title=API_CONFIG["title"],
    description=API_CONFIG["description"],
    version=API_CONFIG["version"]
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=SECURITY_CONFIG["allowed_origins"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化组件
email_generator = EmailGenerator()
code_extractor = CodeExtractor()
gmail_monitor = GmailMonitor()

# Pydantic模型
class EmailGenerateRequest(BaseModel):
    app_name: str
    prefix: Optional[str] = None
    custom_suffix: Optional[str] = None

class EmailGenerateResponse(BaseModel):
    success: bool
    email_address: Optional[str] = None
    email_id: Optional[int] = None
    app_name: Optional[str] = None
    prefix: Optional[str] = None
    created_at: Optional[str] = None
    message: str
    error: Optional[str] = None

class CodeResponse(BaseModel):
    success: bool
    code: Optional[str] = None
    code_id: Optional[int] = None
    sender_email: Optional[str] = None
    subject: Optional[str] = None
    extracted_at: Optional[str] = None
    expires_at: Optional[str] = None
    is_used: Optional[bool] = None
    message: str
    error: Optional[str] = None

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logger.info("启动邮箱验证码API服务")
    
    # 创建数据库表
    create_tables()
    logger.info("数据库表初始化完成")

# 健康检查
@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "email_verification_system",
        "version": API_CONFIG["version"]
    }

# 生成邮箱接口
@app.post("/api/generate-email", response_model=EmailGenerateResponse)
async def generate_email(
    request: EmailGenerateRequest,
    db: Session = Depends(get_db)
):
    """
    生成新的邮箱地址
    
    - **app_name**: 应用名称（必填）
    - **prefix**: 自定义前缀（可选）
    - **custom_suffix**: 自定义后缀（可选）
    """
    try:
        result = email_generator.generate_email(
            app_name=request.app_name,
            prefix=request.prefix,
            custom_suffix=request.custom_suffix,
            db=db
        )
        
        if result.get("success"):
            return EmailGenerateResponse(**result)
        else:
            raise HTTPException(status_code=400, detail=result.get("message", "生成邮箱失败"))
            
    except Exception as e:
        logger.error(f"生成邮箱API错误: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

# 获取验证码接口
@app.get("/api/get-code/{email_id}", response_model=CodeResponse)
async def get_verification_code(
    email_id: int,
    include_used: bool = Query(False, description="是否包含已使用的验证码"),
    db: Session = Depends(get_db)
):
    """
    获取指定邮箱的最新验证码
    
    - **email_id**: 邮箱记录ID
    - **include_used**: 是否包含已使用的验证码
    """
    try:
        # 检查邮箱记录是否存在
        email_info = email_generator.get_email_info(email_id, db)
        if not email_info:
            raise HTTPException(status_code=404, detail="邮箱记录不存在")
        
        # 获取最新验证码
        code_info = code_extractor.get_latest_code(email_id, db, include_used)
        
        if not code_info:
            return CodeResponse(
                success=False,
                message="暂无可用的验证码"
            )
        
        return CodeResponse(
            success=True,
            code=code_info.get("code"),
            code_id=code_info.get("id"),
            sender_email=code_info.get("sender_email"),
            subject=code_info.get("subject"),
            extracted_at=code_info.get("extracted_at"),
            expires_at=code_info.get("expires_at"),
            is_used=code_info.get("is_used"),
            message="获取验证码成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取验证码API错误: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

# 标记验证码已使用
@app.post("/api/mark-code-used/{code_id}")
async def mark_code_used(
    code_id: int,
    db: Session = Depends(get_db)
):
    """
    标记验证码为已使用
    
    - **code_id**: 验证码记录ID
    """
    try:
        success = code_extractor.mark_code_used(code_id, db)
        
        if success:
            return {"success": True, "message": "验证码已标记为使用"}
        else:
            raise HTTPException(status_code=404, detail="验证码记录不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"标记验证码使用API错误: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

# 获取邮箱信息
@app.get("/api/email-info/{email_id}")
async def get_email_info(
    email_id: int,
    db: Session = Depends(get_db)
):
    """
    获取邮箱详细信息
    
    - **email_id**: 邮箱记录ID
    """
    try:
        email_info = email_generator.get_email_info(email_id, db)
        
        if not email_info:
            raise HTTPException(status_code=404, detail="邮箱记录不存在")
        
        return {
            "success": True,
            "email_info": email_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取邮箱信息API错误: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

# 列出邮箱记录
@app.get("/api/emails")
async def list_emails(
    app_name: Optional[str] = Query(None, description="按应用名称过滤"),
    is_active: Optional[bool] = Query(None, description="按激活状态过滤"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_db)
):
    """
    列出邮箱记录
    
    - **app_name**: 按应用名称过滤（可选）
    - **is_active**: 按激活状态过滤（可选）
    - **limit**: 返回记录数量限制
    - **offset**: 偏移量
    """
    try:
        result = email_generator.list_emails(
            app_name=app_name,
            is_active=is_active,
            limit=limit,
            offset=offset,
            db=db
        )
        
        return result
        
    except Exception as e:
        logger.error(f"列出邮箱记录API错误: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

# 停用邮箱
@app.post("/api/deactivate-email/{email_id}")
async def deactivate_email(
    email_id: int,
    db: Session = Depends(get_db)
):
    """
    停用邮箱
    
    - **email_id**: 邮箱记录ID
    """
    try:
        success = email_generator.deactivate_email(email_id, db)
        
        if success:
            return {"success": True, "message": "邮箱已停用"}
        else:
            raise HTTPException(status_code=404, detail="邮箱记录不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停用邮箱API错误: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

# 手动检查邮件
@app.post("/api/check-emails")
async def check_emails():
    """
    手动触发邮件检查
    """
    try:
        if not gmail_monitor.authenticate():
            raise HTTPException(status_code=500, detail="Gmail认证失败")
        
        result = gmail_monitor.process_new_messages()
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查邮件API错误: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

# 错误处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"detail": "内部服务器错误"}
    )

if __name__ == "__main__":
    import uvicorn
    
    # 配置日志
    from config.settings import LOGGING_CONFIG
    logger.add(
        LOGGING_CONFIG["log_file"],
        rotation=LOGGING_CONFIG["rotation"],
        retention=LOGGING_CONFIG["retention"],
        format=LOGGING_CONFIG["format"],
        level=LOGGING_CONFIG["level"]
    )
    
    # 启动服务器
    uvicorn.run(
        "api_server:app",
        host=API_CONFIG["host"],
        port=API_CONFIG["port"],
        reload=API_CONFIG["reload"],
        log_level=LOGGING_CONFIG["level"].lower()
    )
