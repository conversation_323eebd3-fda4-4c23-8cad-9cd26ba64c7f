"""
系统配置文件
"""
import os
from pathlib import Path
from typing import List, Dict, Any

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

# Gmail API配置
GMAIL_CONFIG = {
    "credentials_file": PROJECT_ROOT / "config" / "gmail_credentials.json",
    "token_file": PROJECT_ROOT / "config" / "gmail_token.json",
    "scopes": ["https://www.googleapis.com/auth/gmail.readonly"],
    "check_interval": 30,  # 检查新邮件的间隔（秒）
}

# 数据库配置
DATABASE_CONFIG = {
    "url": f"sqlite:///{PROJECT_ROOT}/data/email_verification.db",
    "echo": False,  # 是否打印SQL语句
}

# 邮箱生成配置
EMAIL_CONFIG = {
    "domain": "yourdomain.com",  # 替换为您的域名
    "default_prefix": "verify",
    "separator": "_",
    "timestamp_format": "%Y%m%d%H%M%S",
    "max_length": 64,  # 邮箱地址最大长度
}

# 验证码提取配置
CODE_EXTRACTION_CONFIG = {
    "patterns": [
        # 6位数字验证码
        r"验证码[：:\s]*(\d{6})",
        r"verification code[：:\s]*(\d{6})",
        r"code[：:\s]*(\d{6})",
        # 4位数字验证码
        r"验证码[：:\s]*(\d{4})",
        r"verification code[：:\s]*(\d{4})",
        # 8位数字验证码
        r"验证码[：:\s]*(\d{8})",
        # 字母数字混合验证码
        r"验证码[：:\s]*([A-Z0-9]{6})",
        r"verification code[：:\s]*([A-Z0-9]{6})",
        # 通用模式
        r"(\d{4,8})",
        r"([A-Z0-9]{4,8})",
    ],
    "timeout": 300,  # 验证码有效期（秒）
    "max_attempts": 3,  # 最大提取尝试次数
}

# API服务器配置
API_CONFIG = {
    "host": "0.0.0.0",
    "port": 8000,
    "debug": True,
    "reload": True,
    "title": "邮箱验证码自动处理系统",
    "description": "自动生成邮箱并提取验证码的API服务",
    "version": "1.0.0",
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
    "rotation": "1 day",
    "retention": "30 days",
    "log_file": PROJECT_ROOT / "logs" / "app.log",
}

# 安全配置
SECURITY_CONFIG = {
    "api_key_header": "X-API-Key",
    "rate_limit": {
        "requests_per_minute": 60,
        "requests_per_hour": 1000,
    },
    "allowed_origins": ["*"],  # CORS配置
}

# 邮件过滤配置
EMAIL_FILTER_CONFIG = {
    "allowed_senders": [
        # 添加允许的发件人域名或邮箱
        "<EMAIL>",
        "@verification.com",
        "@auth.service.com",
    ],
    "blocked_senders": [
        # 添加需要屏蔽的发件人
        "<EMAIL>",
    ],
    "subject_keywords": [
        "验证码",
        "verification",
        "code",
        "verify",
        "authentication",
        "登录",
        "注册",
    ],
}

# 缓存配置
CACHE_CONFIG = {
    "type": "memory",  # 可选: memory, redis
    "ttl": 3600,  # 缓存过期时间（秒）
    "max_size": 1000,  # 最大缓存条目数
}

# 监控配置
MONITORING_CONFIG = {
    "health_check_interval": 60,  # 健康检查间隔（秒）
    "metrics_enabled": True,
    "alert_email": "<EMAIL>",
}

# 环境变量覆盖
def load_env_config():
    """从环境变量加载配置覆盖"""
    env_overrides = {}
    
    # Gmail配置覆盖
    if os.getenv("GMAIL_CREDENTIALS_FILE"):
        GMAIL_CONFIG["credentials_file"] = os.getenv("GMAIL_CREDENTIALS_FILE")
    
    # 数据库配置覆盖
    if os.getenv("DATABASE_URL"):
        DATABASE_CONFIG["url"] = os.getenv("DATABASE_URL")
    
    # 邮箱域名覆盖
    if os.getenv("EMAIL_DOMAIN"):
        EMAIL_CONFIG["domain"] = os.getenv("EMAIL_DOMAIN")
    
    # API配置覆盖
    if os.getenv("API_HOST"):
        API_CONFIG["host"] = os.getenv("API_HOST")
    if os.getenv("API_PORT"):
        API_CONFIG["port"] = int(os.getenv("API_PORT"))
    
    return env_overrides

# 加载环境变量配置
load_env_config()

# 创建必要的目录
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        PROJECT_ROOT / "data",
        PROJECT_ROOT / "logs",
        PROJECT_ROOT / "config",
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

# 初始化时创建目录
ensure_directories()
