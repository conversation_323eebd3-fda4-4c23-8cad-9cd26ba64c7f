#!/usr/bin/env python3
"""
邮箱转发测试脚本
"""

import smtplib
import requests
import time
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime

API_BASE = "http://localhost:8000"

def generate_test_email():
    """生成测试邮箱"""
    print("📧 生成测试邮箱...")
    try:
        data = {
            "app_name": "forwarding_test",
            "prefix": "test"
        }
        response = requests.post(f"{API_BASE}/api/generate-email", json=data)
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                email_address = result.get('email_address')
                email_id = result.get('email_id')
                print(f"✅ 测试邮箱生成成功:")
                print(f"   邮箱地址: {email_address}")
                print(f"   邮箱ID: {email_id}")
                return email_address, email_id
            else:
                print(f"❌ 邮箱生成失败: {result.get('message')}")
                return None, None
        else:
            print(f"❌ 邮箱生成请求失败: {response.status_code}")
            return None, None
    except Exception as e:
        print(f"❌ 邮箱生成异常: {e}")
        return None, None

def create_test_verification_email(to_email, verification_code="123456"):
    """创建测试验证码邮件"""
    msg = MIMEMultipart('alternative')
    msg['Subject'] = "【616866.xyz】邮箱转发测试 - 验证码"
    msg['From'] = "<EMAIL>"
    msg['To'] = to_email
    
    # 纯文本版本
    text_content = f"""
邮箱转发测试

您的验证码是: {verification_code}

这是一封测试邮件，用于验证域名邮箱转发功能是否正常工作。

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
目标邮箱: {to_email}
域名: 616866.xyz
转发邮箱: <EMAIL>

如果您收到这封邮件，说明邮箱转发配置正确。
    """
    
    # HTML版本
    html_content = f"""
    <html>
    <body>
        <h2>📧 邮箱转发测试</h2>
        <p>您的验证码是: <strong style="font-size: 24px; color: #007bff;">{verification_code}</strong></p>
        
        <p>这是一封测试邮件，用于验证域名邮箱转发功能是否正常工作。</p>
        
        <hr>
        <p><strong>测试信息:</strong></p>
        <ul>
            <li>时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
            <li>目标邮箱: {to_email}</li>
            <li>域名: 616866.xyz</li>
            <li>转发邮箱: <EMAIL></li>
        </ul>
        
        <p style="color: green;">✅ 如果您收到这封邮件，说明邮箱转发配置正确。</p>
    </body>
    </html>
    """
    
    # 添加文本和HTML部分
    part1 = MIMEText(text_content, 'plain', 'utf-8')
    part2 = MIMEText(html_content, 'html', 'utf-8')
    
    msg.attach(part1)
    msg.attach(part2)
    
    return msg

def send_test_email_via_gmail(to_email, verification_code="123456"):
    """通过Gmail发送测试邮件（模拟外部服务发送验证码）"""
    print(f"📤 发送测试验证码邮件到: {to_email}")
    print(f"🔢 验证码: {verification_code}")
    
    # 这里需要您手动发送邮件，或者配置SMTP服务器
    print("""
⚠️  请手动发送测试邮件：

1. 打开您的邮箱客户端或网页邮箱
2. 发送邮件到: {to_email}
3. 邮件主题: 【测试】验证码
4. 邮件内容包含: 验证码: {verification_code}

或者复制以下内容发送：
---
主题: 【测试】验证码

您的验证码是: {verification_code}

这是邮箱转发功能测试。
---
    """.format(to_email=to_email, verification_code=verification_code))
    
    return True

def wait_for_verification_code(email_id, timeout=300):
    """等待验证码到达"""
    print(f"⏳ 等待验证码到达 (最多等待 {timeout} 秒)...")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{API_BASE}/api/get-code/{email_id}")
            if response.status_code == 200:
                result = response.json()
                if result.get("success") and result.get("code"):
                    print(f"✅ 验证码接收成功: {result.get('code')}")
                    print(f"📧 发件人: {result.get('sender_email')}")
                    print(f"📝 主题: {result.get('subject')}")
                    print(f"⏰ 提取时间: {result.get('extracted_at')}")
                    return result.get('code')
            
            # 每10秒检查一次
            print(f"⏳ 等待中... ({int(time.time() - start_time)}s)")
            time.sleep(10)
            
        except Exception as e:
            print(f"❌ 检查验证码时出错: {e}")
            time.sleep(10)
    
    print(f"⏰ 等待超时 ({timeout}秒)，未收到验证码")
    return None

def test_forwarding_complete():
    """完整的转发测试流程"""
    print("🚀 开始邮箱转发测试...\n")
    
    # 1. 生成测试邮箱
    email_address, email_id = generate_test_email()
    if not email_address:
        print("❌ 测试失败：无法生成邮箱")
        return False
    
    print(f"\n📋 测试信息:")
    print(f"   生成的邮箱: {email_address}")
    print(f"   邮箱ID: {email_id}")
    print(f"   域名: 616866.xyz")
    print(f"   转发目标: <EMAIL>")
    
    # 2. 生成验证码
    verification_code = f"{int(time.time()) % 1000000:06d}"  # 6位数字验证码
    
    # 3. 发送测试邮件
    print(f"\n📤 请发送测试邮件:")
    print(f"   收件人: {email_address}")
    print(f"   主题: 【测试】验证码")
    print(f"   内容: 您的验证码是: {verification_code}")
    
    input("\n按回车键继续，当您已经发送了测试邮件...")
    
    # 4. 等待验证码
    received_code = wait_for_verification_code(email_id, timeout=300)
    
    # 5. 验证结果
    if received_code:
        if received_code == verification_code:
            print(f"\n🎉 测试完全成功！")
            print(f"   发送的验证码: {verification_code}")
            print(f"   接收的验证码: {received_code}")
            print(f"   ✅ 验证码匹配")
            return True
        else:
            print(f"\n⚠️ 测试部分成功！")
            print(f"   发送的验证码: {verification_code}")
            print(f"   接收的验证码: {received_code}")
            print(f"   ❌ 验证码不匹配，但邮件转发正常")
            return True
    else:
        print(f"\n❌ 测试失败：未收到验证码")
        print(f"\n🔍 可能的原因:")
        print(f"   1. 域名邮箱转发未配置")
        print(f"   2. 邮件被Gmail标记为垃圾邮件")
        print(f"   3. Gmail API未配置")
        print(f"   4. 邮件发送失败")
        return False

def main():
    """主函数"""
    print("📧 邮箱转发功能测试工具")
    print("=" * 50)
    
    # 检查API服务器
    try:
        response = requests.get(f"{API_BASE}/health")
        if response.status_code != 200:
            print("❌ API服务器未运行，请先启动API服务器")
            return
    except:
        print("❌ 无法连接到API服务器，请确保服务器正在运行")
        return
    
    # 运行测试
    success = test_forwarding_complete()
    
    if success:
        print(f"\n✅ 邮箱转发测试完成")
        print(f"\n📝 下一步:")
        print(f"   1. 配置Gmail API (参考 GMAIL_SETUP.md)")
        print(f"   2. 启动Gmail监听服务")
        print(f"   3. 测试自动验证码提取")
    else:
        print(f"\n❌ 邮箱转发测试失败")
        print(f"\n🔧 请检查:")
        print(f"   1. 域名DNS设置")
        print(f"   2. 邮箱转发规则")
        print(f"   3. Gmail收件箱和垃圾邮件文件夹")

if __name__ == "__main__":
    main()
