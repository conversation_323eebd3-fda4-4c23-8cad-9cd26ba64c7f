"""
邮箱生成器测试
"""

import pytest
from unittest.mock import Mock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.email_generator import EmailGenerator
from src.models.base import Base
from src.models.email_record import EmailRecord

# 测试数据库配置
TEST_DATABASE_URL = "sqlite:///:memory:"

@pytest.fixture
def test_db():
    """创建测试数据库"""
    engine = create_engine(TEST_DATABASE_URL)
    Base.metadata.create_all(engine)
    
    TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = TestSessionLocal()
    
    yield session
    
    session.close()
    Base.metadata.drop_all(engine)

@pytest.fixture
def email_generator():
    """创建邮箱生成器实例"""
    return EmailGenerator()

class TestEmailGenerator:
    """邮箱生成器测试类"""
    
    def test_generate_email_success(self, email_generator, test_db):
        """测试成功生成邮箱"""
        result = email_generator.generate_email(
            app_name="test_app",
            prefix="test",
            db=test_db
        )
        
        assert result["success"] is True
        assert "@" in result["email_address"]
        assert result["app_name"] == "test_app"
        assert result["email_id"] is not None
    
    def test_generate_email_without_db(self, email_generator):
        """测试不使用数据库生成邮箱"""
        result = email_generator.generate_email(
            app_name="test_app",
            prefix="test"
        )
        
        assert result["success"] is True
        assert "@" in result["email_address"]
        assert result["email_id"] is None
    
    def test_generate_email_with_custom_suffix(self, email_generator, test_db):
        """测试使用自定义后缀生成邮箱"""
        result = email_generator.generate_email(
            app_name="test_app",
            custom_suffix="custom",
            db=test_db
        )
        
        assert result["success"] is True
        assert "custom" in result["email_address"]
    
    def test_clean_string(self, email_generator):
        """测试字符串清理功能"""
        # 测试特殊字符清理
        cleaned = email_generator._clean_string("Test@App#123!")
        assert cleaned == "testapp123"
        
        # 测试连字符处理
        cleaned = email_generator._clean_string("test--app")
        assert cleaned == "test-app"
        
        # 测试开头结尾连字符
        cleaned = email_generator._clean_string("-test-app-")
        assert cleaned == "test-app"
    
    def test_validate_email(self, email_generator):
        """测试邮箱格式验证"""
        # 有效邮箱
        assert email_generator._validate_email("<EMAIL>") is True
        assert email_generator._validate_email("<EMAIL>") is True
        
        # 无效邮箱
        assert email_generator._validate_email("invalid-email") is False
        assert email_generator._validate_email("@domain.com") is False
        assert email_generator._validate_email("user@") is False
    
    def test_get_email_info(self, email_generator, test_db):
        """测试获取邮箱信息"""
        # 先创建一个邮箱记录
        result = email_generator.generate_email(
            app_name="test_app",
            db=test_db
        )
        
        email_id = result["email_id"]
        
        # 获取邮箱信息
        info = email_generator.get_email_info(email_id, test_db)
        
        assert info is not None
        assert info["id"] == email_id
        assert info["app_name"] == "test_app"
    
    def test_deactivate_email(self, email_generator, test_db):
        """测试停用邮箱"""
        # 先创建一个邮箱记录
        result = email_generator.generate_email(
            app_name="test_app",
            db=test_db
        )
        
        email_id = result["email_id"]
        
        # 停用邮箱
        success = email_generator.deactivate_email(email_id, test_db)
        assert success is True
        
        # 验证邮箱已停用
        info = email_generator.get_email_info(email_id, test_db)
        assert info["is_active"] is False
    
    def test_list_emails(self, email_generator, test_db):
        """测试列出邮箱记录"""
        # 创建多个邮箱记录
        email_generator.generate_email(app_name="app1", db=test_db)
        email_generator.generate_email(app_name="app2", db=test_db)
        email_generator.generate_email(app_name="app1", db=test_db)
        
        # 列出所有邮箱
        result = email_generator.list_emails(db=test_db)
        assert result["success"] is True
        assert result["total"] == 3
        assert len(result["emails"]) == 3
        
        # 按应用名称过滤
        result = email_generator.list_emails(app_name="app1", db=test_db)
        assert result["total"] == 2
        
        # 测试分页
        result = email_generator.list_emails(limit=2, offset=0, db=test_db)
        assert len(result["emails"]) == 2
    
    def test_duplicate_email_handling(self, email_generator, test_db):
        """测试重复邮箱处理"""
        # 模拟邮箱地址冲突的情况
        with patch.object(email_generator, '_clean_string', return_value="fixed"):
            with patch('uuid.uuid4') as mock_uuid:
                mock_uuid.return_value.hex = "12345678"
                
                # 第一次生成
                result1 = email_generator.generate_email(
                    app_name="test",
                    db=test_db
                )
                
                # 第二次生成应该成功（因为会重新生成）
                result2 = email_generator.generate_email(
                    app_name="test",
                    db=test_db
                )
                
                assert result1["success"] is True
                assert result2["success"] is True
                # 邮箱地址应该不同
                assert result1["email_address"] != result2["email_address"]

if __name__ == "__main__":
    pytest.main([__file__])
