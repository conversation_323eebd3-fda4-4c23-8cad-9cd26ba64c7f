# Gmail API 配置指南

## 概述

为了让系统能够自动监听和处理转发到 `<EMAIL>` 的验证码邮件，需要配置Gmail API。

## 前提条件

- 域名：`616866.xyz`
- 转发邮箱：`<EMAIL>`
- 邮箱转发规则：`*@616866.xyz` → `<EMAIL>`

## 步骤1：创建Google Cloud项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 点击"选择项目" → "新建项目"
3. 输入项目名称：`email-verification-system`
4. 点击"创建"

## 步骤2：启用Gmail API

1. 在Google Cloud Console中，转到"API和服务" → "库"
2. 搜索"Gmail API"
3. 点击"Gmail API"
4. 点击"启用"

## 步骤3：配置OAuth同意屏幕

1. 转到"API和服务" → "OAuth同意屏幕"
2. 选择"外部"用户类型
3. 填写应用信息：
   - 应用名称：`邮箱验证码系统`
   - 用户支持电子邮件：`<EMAIL>`
   - 开发者联系信息：`<EMAIL>`
4. 添加作用域：
   - 点击"添加或移除作用域"
   - 搜索并添加：`https://www.googleapis.com/auth/gmail.readonly`
5. 添加测试用户：
   - 在"测试用户"部分添加：`<EMAIL>`

## 步骤4：创建OAuth 2.0凭据

1. 转到"API和服务" → "凭据"
2. 点击"创建凭据" → "OAuth 2.0客户端ID"
3. 选择应用类型：`桌面应用程序`
4. 名称：`邮箱验证码客户端`
5. 点击"创建"
6. 下载JSON文件

## 步骤5：配置凭据文件

1. 将下载的JSON文件重命名为 `gmail_credentials.json`
2. 将文件放入项目的 `config/` 目录
3. 文件路径应为：`config/gmail_credentials.json`

## 步骤6：域名邮箱转发设置

### 在您的域名管理面板中设置：

1. **通配符转发**（推荐）：
   ```
   *@616866.xyz → <EMAIL>
   ```

2. **或者特定前缀转发**：
   ```
   <EMAIL> → <EMAIL>
   <EMAIL> → <EMAIL>
   <EMAIL> → <EMAIL>
   ```

### 测试邮箱转发

发送测试邮件到生成的邮箱地址，确认能收到转发邮件：
```
<EMAIL>
```

## 步骤7：启动Gmail监听服务

配置完成后，启动Gmail监听服务：

```bash
# 在新的终端窗口中运行
python src/gmail_monitor.py
```

首次运行时会打开浏览器进行OAuth授权。

## 步骤8：测试完整流程

1. **生成邮箱**：
   ```bash
   curl -X POST "http://localhost:8000/api/generate-email" \
        -H "Content-Type: application/json" \
        -d '{"app_name": "test", "prefix": "demo"}'
   ```

2. **发送测试邮件**：
   向生成的邮箱地址发送包含验证码的邮件

3. **检查验证码**：
   ```bash
   curl "http://localhost:8000/api/get-code/{email_id}"
   ```

## 常见问题

### Q: OAuth授权失败
**A**: 确保：
- Gmail API已启用
- OAuth同意屏幕配置正确
- 测试用户已添加
- 凭据文件路径正确

### Q: 收不到转发邮件
**A**: 检查：
- 域名DNS设置
- 邮箱转发规则
- Gmail垃圾邮件文件夹

### Q: 验证码提取失败
**A**: 检查：
- 邮件内容格式
- 正则表达式模式
- 日志文件错误信息

## 安全注意事项

1. **凭据文件安全**：
   - 不要将 `gmail_credentials.json` 提交到版本控制
   - 设置适当的文件权限

2. **API配额**：
   - Gmail API有使用限制
   - 合理设置检查间隔

3. **数据隐私**：
   - 定期清理过期的验证码
   - 不要存储敏感邮件内容

## 配置文件示例

### gmail_credentials.json 格式：
```json
{
  "installed": **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
}
```

## 支持

如果遇到问题：
1. 查看日志文件：`logs/app.log`
2. 检查Gmail API配额使用情况
3. 验证域名邮箱转发是否正常工作
