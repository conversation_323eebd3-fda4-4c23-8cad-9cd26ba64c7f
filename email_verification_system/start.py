#!/usr/bin/env python3
"""
系统启动脚本
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def setup_environment():
    """设置环境"""
    # 创建必要的目录
    directories = ["data", "logs", "config"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    # 检查配置文件
    env_file = Path(".env")
    if not env_file.exists():
        print("警告: .env文件不存在，请复制.env.example并配置")
        return False
    
    # 检查Gmail凭证文件
    credentials_file = Path("config/gmail_credentials.json")
    if not credentials_file.exists():
        print("警告: Gmail凭证文件不存在，请配置Gmail API")
        print("请参考README.md中的Gmail API配置说明")
        return False
    
    return True

def install_dependencies():
    """安装依赖"""
    print("安装Python依赖...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        print("依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"依赖安装失败: {e}")
        return False

def init_database():
    """初始化数据库"""
    print("初始化数据库...")
    try:
        from src.database import db_manager
        db_manager.init_database()
        print("数据库初始化完成")
        return True
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        return False

def start_api_server():
    """启动API服务器"""
    print("启动API服务器...")
    try:
        subprocess.run([sys.executable, "src/api_server.py"], check=True)
    except KeyboardInterrupt:
        print("API服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"API服务器启动失败: {e}")

def start_gmail_monitor():
    """启动Gmail监听服务"""
    print("启动Gmail监听服务...")
    try:
        subprocess.run([sys.executable, "src/gmail_monitor.py"], check=True)
    except KeyboardInterrupt:
        print("Gmail监听服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"Gmail监听服务启动失败: {e}")

def run_tests():
    """运行测试"""
    print("运行测试...")
    try:
        subprocess.run([sys.executable, "-m", "pytest", "tests/", "-v"], check=True)
        print("测试完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"测试失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="邮箱验证码系统启动脚本")
    parser.add_argument("command", choices=["setup", "install", "init-db", "api", "monitor", "test", "all"],
                       help="要执行的命令")
    
    args = parser.parse_args()
    
    if args.command == "setup":
        if setup_environment():
            print("环境设置完成")
        else:
            print("环境设置失败")
            sys.exit(1)
    
    elif args.command == "install":
        if not install_dependencies():
            sys.exit(1)
    
    elif args.command == "init-db":
        if not init_database():
            sys.exit(1)
    
    elif args.command == "api":
        start_api_server()
    
    elif args.command == "monitor":
        start_gmail_monitor()
    
    elif args.command == "test":
        if not run_tests():
            sys.exit(1)
    
    elif args.command == "all":
        # 完整启动流程
        if not setup_environment():
            sys.exit(1)
        
        if not install_dependencies():
            sys.exit(1)
        
        if not init_database():
            sys.exit(1)
        
        print("系统初始化完成，请手动启动服务:")
        print("1. 启动API服务器: python start.py api")
        print("2. 启动Gmail监听: python start.py monitor")

if __name__ == "__main__":
    main()
