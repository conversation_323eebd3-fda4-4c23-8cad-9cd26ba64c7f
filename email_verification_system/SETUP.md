# 快速设置指南

## 1. 环境准备

### 系统要求
- Python 3.8+
- Gmail账号
- 域名邮箱转发功能

### 安装依赖
```bash
cd email_verification_system
pip install -r requirements.txt
```

## 2. Gmail API配置

### 步骤1: 创建Google Cloud项目
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用Gmail API

### 步骤2: 创建凭证
1. 在Google Cloud Console中，转到"凭据"页面
2. 点击"创建凭据" > "OAuth 2.0客户端ID"
3. 选择应用类型为"桌面应用程序"
4. 下载JSON凭证文件
5. 将文件重命名为`gmail_credentials.json`并放入`config/`目录

### 步骤3: 配置OAuth同意屏幕
1. 在Google Cloud Console中配置OAuth同意屏幕
2. 添加测试用户（您的Gmail地址）
3. 添加必要的作用域：`https://www.googleapis.com/auth/gmail.readonly`

## 3. 系统配置

### 复制环境配置
```bash
cp .env.example .env
```

### 编辑.env文件
```bash
# 替换为您的域名
EMAIL_DOMAIN=yourdomain.com

# API配置
API_HOST=0.0.0.0
API_PORT=8000

# 其他配置保持默认
```

### 编辑config/settings.py
确保域名配置正确：
```python
EMAIL_CONFIG = {
    "domain": "yourdomain.com",  # 替换为您的实际域名
    # ... 其他配置
}
```

## 4. 域名邮箱转发设置

### 配置邮箱转发规则
在您的域名管理面板中设置：
- 将 `*@yourdomain.com` 转发到您的Gmail地址
- 或者设置特定前缀的邮箱转发规则

### 测试转发功能
1. 发送测试邮件到 `<EMAIL>`
2. 确认邮件能正确转发到Gmail

## 5. 初始化系统

### 使用启动脚本
```bash
# 完整初始化
python start.py all

# 或者分步执行
python start.py setup      # 环境设置
python start.py install    # 安装依赖
python start.py init-db    # 初始化数据库
```

### 手动初始化
```bash
# 创建目录
mkdir -p data logs config

# 初始化数据库
python -c "from src.database import db_manager; db_manager.init_database()"
```

## 6. 启动服务

### 方式1: 分别启动服务
```bash
# 终端1: 启动API服务器
python src/api_server.py

# 终端2: 启动Gmail监听服务
python src/gmail_monitor.py
```

### 方式2: 使用Docker
```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f
```

## 7. 测试系统

### 运行单元测试
```bash
python start.py test
# 或者
pytest tests/ -v
```

### 手动测试
1. 打开浏览器访问 `http://localhost:8000/health`
2. 访问Web界面 `file:///path/to/web/index.html`
3. 生成测试邮箱
4. 发送验证码邮件到生成的邮箱
5. 检查是否能正确提取验证码

## 8. API使用示例

### 生成邮箱
```bash
curl -X POST "http://localhost:8000/api/generate-email" \
     -H "Content-Type: application/json" \
     -d '{"app_name": "test_app", "prefix": "demo"}'
```

### 获取验证码
```bash
curl "http://localhost:8000/api/get-code/1"
```

### 查看API文档
访问 `http://localhost:8000/docs` 查看完整的API文档

## 9. 常见问题

### Gmail认证失败
- 确保Gmail API已启用
- 检查凭证文件路径和格式
- 确认OAuth同意屏幕配置正确

### 邮箱转发不工作
- 检查域名DNS设置
- 确认邮箱转发规则配置正确
- 测试邮件是否能到达Gmail

### 验证码提取失败
- 检查邮件内容格式
- 调整正则表达式模式
- 查看日志文件了解详细错误

### 数据库问题
- 确保data目录有写权限
- 检查SQLite数据库文件
- 重新初始化数据库

## 10. 生产环境部署

### 安全配置
- 设置强密码和API密钥
- 配置HTTPS
- 限制API访问

### 监控和日志
- 配置日志轮转
- 设置监控告警
- 定期备份数据库

### 性能优化
- 调整检查间隔
- 配置缓存
- 优化数据库查询

## 支持

如果遇到问题，请：
1. 查看日志文件 `logs/app.log`
2. 检查配置文件
3. 运行测试确认功能
4. 参考API文档
