#!/usr/bin/env python3
"""
Gmail API连接测试脚本
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

def test_gmail_connection():
    """测试Gmail API连接"""
    print("🔍 测试Gmail API连接...")
    
    try:
        from src.gmail_monitor import GmailMonitor
        
        # 创建Gmail监听器实例
        monitor = GmailMonitor()
        
        # 尝试认证
        print("🔐 尝试Gmail API认证...")
        if monitor.authenticate():
            print("✅ Gmail API认证成功！")
            
            # 测试获取邮件
            print("📧 测试获取最近邮件...")
            try:
                messages = monitor.get_recent_messages(max_results=5)
                print(f"✅ 成功获取 {len(messages)} 封最近邮件")
                
                if messages:
                    print("\n📋 最近邮件列表:")
                    for i, msg in enumerate(messages[:3], 1):
                        print(f"   {i}. 主题: {msg.get('subject', '无主题')[:50]}")
                        print(f"      发件人: {msg.get('sender', '未知')}")
                        print(f"      日期: {msg.get('date', '未知')}")
                        print()
                
                return True
                
            except Exception as e:
                print(f"❌ 获取邮件失败: {str(e)}")
                return False
                
        else:
            print("❌ Gmail API认证失败")
            return False
            
    except ImportError as e:
        print(f"❌ 导入Gmail监听器失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Gmail API测试异常: {str(e)}")
        return False

def check_credentials_file():
    """检查凭据文件"""
    print("📁 检查Gmail凭据文件...")
    
    credentials_file = Path("config/gmail_credentials.json")
    
    if not credentials_file.exists():
        print("❌ Gmail凭据文件不存在")
        print(f"   请将下载的JSON文件重命名为 gmail_credentials.json")
        print(f"   并放置在: {credentials_file.absolute()}")
        return False
    
    try:
        import json
        with open(credentials_file, 'r', encoding='utf-8') as f:
            creds = json.load(f)
        
        # 检查文件格式
        if 'installed' in creds:
            client_id = creds['installed'].get('client_id', '')
            if client_id.startswith('733279784952-'):
                print("✅ Gmail凭据文件格式正确")
                print(f"   客户端ID: {client_id[:20]}...")
                return True
            else:
                print("❌ 客户端ID不匹配")
                return False
        else:
            print("❌ Gmail凭据文件格式错误")
            return False
            
    except Exception as e:
        print(f"❌ 读取凭据文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 Gmail API配置测试")
    print("=" * 50)
    
    # 1. 检查凭据文件
    if not check_credentials_file():
        print("\n❌ 凭据文件检查失败，请先配置Gmail凭据文件")
        return 1
    
    # 2. 测试Gmail连接
    if test_gmail_connection():
        print("\n🎉 Gmail API配置测试成功！")
        print("\n📝 下一步:")
        print("   1. 配置域名邮箱转发: *@616866.xyz → <EMAIL>")
        print("   2. 运行邮箱转发测试: python test_email_forwarding.py")
        print("   3. 启动Gmail监听服务: python src/gmail_monitor.py")
        return 0
    else:
        print("\n❌ Gmail API配置测试失败")
        print("\n🔧 可能的解决方案:")
        print("   1. 检查Gmail API是否已启用")
        print("   2. 检查OAuth同意屏幕配置")
        print("   3. 确认测试用户已添加")
        print("   4. 重新下载凭据文件")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 测试过程中发生异常: {e}")
        sys.exit(1)
