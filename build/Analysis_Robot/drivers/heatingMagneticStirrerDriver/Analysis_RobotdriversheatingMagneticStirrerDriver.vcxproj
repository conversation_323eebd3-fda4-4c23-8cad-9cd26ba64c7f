<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{07D278DE-7064-3024-94B7-53A1FEC8E5D3}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Analysis_RobotdriversheatingMagneticStirrerDriver</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\newFuxios\build\Analysis_Robot\drivers\heatingMagneticStirrerDriver\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Analysis_RobotdriversheatingMagneticStirrerDriver.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Analysis_RobotdriversheatingMagneticStirrerDriver</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\newFuxios\build\Analysis_Robot\drivers\heatingMagneticStirrerDriver\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Analysis_RobotdriversheatingMagneticStirrerDriver.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Analysis_RobotdriversheatingMagneticStirrerDriver</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\newFuxios\build\Analysis_Robot\drivers\heatingMagneticStirrerDriver\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Analysis_RobotdriversheatingMagneticStirrerDriver.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Analysis_RobotdriversheatingMagneticStirrerDriver</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\newFuxios\build\Analysis_Robot\drivers\heatingMagneticStirrerDriver\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Analysis_RobotdriversheatingMagneticStirrerDriver.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Analysis_RobotdriversheatingMagneticStirrerDriver</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include;C:\opt\glog\include;C:\opt\libmodbus\include;D:\newFuxios\fuxicommon\include;C:\opt\openssl\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/opt/PCL/3rdParty/Boost/include/boost-1_78" /external:I "C:/opt/rttr/include" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_DATE_TIME_NO_LIB;BOOST_IOSTREAMS_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_THREAD_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_PROGRAM_OPTIONS_NO_LIB;RTTR_DLL;QT_CORE_LIB;QT_GUI_LIB;QT_SQL_LIB;QT_WIDGETS_LIB;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_DATE_TIME_NO_LIB;BOOST_IOSTREAMS_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_THREAD_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_PROGRAM_OPTIONS_NO_LIB;RTTR_DLL;QT_CORE_LIB;QT_GUI_LIB;QT_SQL_LIB;QT_WIDGETS_LIB;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include;C:\opt\glog\include;C:\opt\libmodbus\include;D:\newFuxios\fuxicommon\include;C:\opt\openssl\include;C:\opt\PCL\3rdParty\Boost\include\boost-1_78;C:\opt\rttr\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include;C:\opt\glog\include;C:\opt\libmodbus\include;D:\newFuxios\fuxicommon\include;C:\opt\openssl\include;C:\opt\PCL\3rdParty\Boost\include\boost-1_78;C:\opt\rttr\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include;C:\opt\glog\include;C:\opt\libmodbus\include;D:\newFuxios\fuxicommon\include;C:\opt\openssl\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/opt/PCL/3rdParty/Boost/include/boost-1_78" /external:I "C:/opt/rttr/include" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_DATE_TIME_NO_LIB;BOOST_IOSTREAMS_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_THREAD_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_PROGRAM_OPTIONS_NO_LIB;RTTR_DLL;QT_CORE_LIB;QT_NO_DEBUG;QT_GUI_LIB;QT_SQL_LIB;QT_WIDGETS_LIB;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_DATE_TIME_NO_LIB;BOOST_IOSTREAMS_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_THREAD_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_PROGRAM_OPTIONS_NO_LIB;RTTR_DLL;QT_CORE_LIB;QT_NO_DEBUG;QT_GUI_LIB;QT_SQL_LIB;QT_WIDGETS_LIB;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include;C:\opt\glog\include;C:\opt\libmodbus\include;D:\newFuxios\fuxicommon\include;C:\opt\openssl\include;C:\opt\PCL\3rdParty\Boost\include\boost-1_78;C:\opt\rttr\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include;C:\opt\glog\include;C:\opt\libmodbus\include;D:\newFuxios\fuxicommon\include;C:\opt\openssl\include;C:\opt\PCL\3rdParty\Boost\include\boost-1_78;C:\opt\rttr\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include;C:\opt\glog\include;C:\opt\libmodbus\include;D:\newFuxios\fuxicommon\include;C:\opt\openssl\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/opt/PCL/3rdParty/Boost/include/boost-1_78" /external:I "C:/opt/rttr/include" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_DATE_TIME_NO_LIB;BOOST_IOSTREAMS_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_THREAD_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_PROGRAM_OPTIONS_NO_LIB;RTTR_DLL;QT_CORE_LIB;QT_NO_DEBUG;QT_GUI_LIB;QT_SQL_LIB;QT_WIDGETS_LIB;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_DATE_TIME_NO_LIB;BOOST_IOSTREAMS_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_THREAD_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_PROGRAM_OPTIONS_NO_LIB;RTTR_DLL;QT_CORE_LIB;QT_NO_DEBUG;QT_GUI_LIB;QT_SQL_LIB;QT_WIDGETS_LIB;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include;C:\opt\glog\include;C:\opt\libmodbus\include;D:\newFuxios\fuxicommon\include;C:\opt\openssl\include;C:\opt\PCL\3rdParty\Boost\include\boost-1_78;C:\opt\rttr\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include;C:\opt\glog\include;C:\opt\libmodbus\include;D:\newFuxios\fuxicommon\include;C:\opt\openssl\include;C:\opt\PCL\3rdParty\Boost\include\boost-1_78;C:\opt\rttr\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include;C:\opt\glog\include;C:\opt\libmodbus\include;D:\newFuxios\fuxicommon\include;C:\opt\openssl\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/opt/PCL/3rdParty/Boost/include/boost-1_78" /external:I "C:/opt/rttr/include" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql" /external:I "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_DATE_TIME_NO_LIB;BOOST_IOSTREAMS_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_THREAD_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_PROGRAM_OPTIONS_NO_LIB;RTTR_DLL;QT_CORE_LIB;QT_NO_DEBUG;QT_GUI_LIB;QT_SQL_LIB;QT_WIDGETS_LIB;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BOOST_SYSTEM_NO_LIB;BOOST_FILESYSTEM_NO_LIB;BOOST_ATOMIC_NO_LIB;BOOST_DATE_TIME_NO_LIB;BOOST_IOSTREAMS_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_THREAD_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_PROGRAM_OPTIONS_NO_LIB;RTTR_DLL;QT_CORE_LIB;QT_NO_DEBUG;QT_GUI_LIB;QT_SQL_LIB;QT_WIDGETS_LIB;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include;C:\opt\glog\include;C:\opt\libmodbus\include;D:\newFuxios\fuxicommon\include;C:\opt\openssl\include;C:\opt\PCL\3rdParty\Boost\include\boost-1_78;C:\opt\rttr\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include;C:\opt\glog\include;C:\opt\libmodbus\include;D:\newFuxios\fuxicommon\include;C:\opt\openssl\include;C:\opt\PCL\3rdParty\Boost\include\boost-1_78;C:\opt\rttr\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\.\mkspecs\win32-msvc;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtSql;C:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/newFuxios/Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/newFuxios -BD:/newFuxios/build --check-stamp-file D:/newFuxios/build/Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfig.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfigVersion.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\BoostDetectToolset-1.78.0.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-x64-1_78-static.cmake;D:\newFuxios\builder\cmake\add_boost.cmake;D:\newFuxios\builder\cmake\add_glog.cmake;D:\newFuxios\builder\cmake\add_libmodbus.cmake;D:\newFuxios\builder\cmake\common.cmake;D:\newFuxios\builder\cmake\library.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\newFuxios\build\Analysis_Robot\drivers\heatingMagneticStirrerDriver\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/newFuxios/Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/newFuxios -BD:/newFuxios/build --check-stamp-file D:/newFuxios/build/Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfig.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfigVersion.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\BoostDetectToolset-1.78.0.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-x64-1_78-static.cmake;D:\newFuxios\builder\cmake\add_boost.cmake;D:\newFuxios\builder\cmake\add_glog.cmake;D:\newFuxios\builder\cmake\add_libmodbus.cmake;D:\newFuxios\builder\cmake\common.cmake;D:\newFuxios\builder\cmake\library.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\newFuxios\build\Analysis_Robot\drivers\heatingMagneticStirrerDriver\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/newFuxios/Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/newFuxios -BD:/newFuxios/build --check-stamp-file D:/newFuxios/build/Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfig.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfigVersion.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\BoostDetectToolset-1.78.0.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-x64-1_78-static.cmake;D:\newFuxios\builder\cmake\add_boost.cmake;D:\newFuxios\builder\cmake\add_glog.cmake;D:\newFuxios\builder\cmake\add_libmodbus.cmake;D:\newFuxios\builder\cmake\common.cmake;D:\newFuxios\builder\cmake\library.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\newFuxios\build\Analysis_Robot\drivers\heatingMagneticStirrerDriver\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/newFuxios/Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/newFuxios -BD:/newFuxios/build --check-stamp-file D:/newFuxios/build/Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfig.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfigVersion.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\BoostDetectToolset-1.78.0.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-x64-1_78-static.cmake;D:\newFuxios\builder\cmake\add_boost.cmake;D:\newFuxios\builder\cmake\add_glog.cmake;D:\newFuxios\builder\cmake\add_libmodbus.cmake;D:\newFuxios\builder\cmake\common.cmake;D:\newFuxios\builder\cmake\library.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\newFuxios\build\Analysis_Robot\drivers\heatingMagneticStirrerDriver\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\src\HeatingMagneticStirrerDriver.cpp" />
    <ClInclude Include="D:\newFuxios\Analysis_Robot\drivers\heatingMagneticStirrerDriver\include\HeatingMagneticStirrerDriver.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\newFuxios\build\ZERO_CHECK.vcxproj">
      <Project>{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\fuxicommon\fuxicommon.vcxproj">
      <Project>{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}</Project>
      <Name>fuxicommon</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>