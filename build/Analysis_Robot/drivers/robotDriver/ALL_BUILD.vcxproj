<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\robotDriver\include;C:\opt\PCL\3rdParty\Boost\include\boost-1_78;C:\opt\glog\include;C:\opt\libmodbus\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\robotDriver\include;C:\opt\PCL\3rdParty\Boost\include\boost-1_78;C:\opt\glog\include;C:\opt\libmodbus\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\robotDriver\include;C:\opt\PCL\3rdParty\Boost\include\boost-1_78;C:\opt\glog\include;C:\opt\libmodbus\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\newFuxios\Analysis_Robot\drivers\robotDriver\include;C:\opt\PCL\3rdParty\Boost\include\boost-1_78;C:\opt\glog\include;C:\opt\libmodbus\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\newFuxios\Analysis_Robot\drivers\robotDriver\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/newFuxios/Analysis_Robot/drivers/robotDriver/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/newFuxios -BD:/newFuxios/build --check-stamp-file D:/newFuxios/build/Analysis_Robot/drivers/robotDriver/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfig.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfigVersion.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\BoostDetectToolset-1.78.0.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-x64-1_78-static.cmake;D:\newFuxios\builder\cmake\add_boost.cmake;D:\newFuxios\builder\cmake\add_glog.cmake;D:\newFuxios\builder\cmake\add_libmodbus.cmake;D:\newFuxios\builder\cmake\common.cmake;D:\newFuxios\builder\cmake\library.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\newFuxios\build\Analysis_Robot\drivers\robotDriver\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/newFuxios/Analysis_Robot/drivers/robotDriver/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/newFuxios -BD:/newFuxios/build --check-stamp-file D:/newFuxios/build/Analysis_Robot/drivers/robotDriver/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfig.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfigVersion.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\BoostDetectToolset-1.78.0.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-x64-1_78-static.cmake;D:\newFuxios\builder\cmake\add_boost.cmake;D:\newFuxios\builder\cmake\add_glog.cmake;D:\newFuxios\builder\cmake\add_libmodbus.cmake;D:\newFuxios\builder\cmake\common.cmake;D:\newFuxios\builder\cmake\library.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\newFuxios\build\Analysis_Robot\drivers\robotDriver\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/newFuxios/Analysis_Robot/drivers/robotDriver/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/newFuxios -BD:/newFuxios/build --check-stamp-file D:/newFuxios/build/Analysis_Robot/drivers/robotDriver/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfig.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfigVersion.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\BoostDetectToolset-1.78.0.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-x64-1_78-static.cmake;D:\newFuxios\builder\cmake\add_boost.cmake;D:\newFuxios\builder\cmake\add_glog.cmake;D:\newFuxios\builder\cmake\add_libmodbus.cmake;D:\newFuxios\builder\cmake\common.cmake;D:\newFuxios\builder\cmake\library.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\newFuxios\build\Analysis_Robot\drivers\robotDriver\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/newFuxios/Analysis_Robot/drivers/robotDriver/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/newFuxios -BD:/newFuxios/build --check-stamp-file D:/newFuxios/build/Analysis_Robot/drivers/robotDriver/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfig.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\Boost-1.78.0\BoostConfigVersion.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\BoostDetectToolset-1.78.0.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\boost_atomic-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_atomic-1.78.0\libboost_atomic-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\boost_chrono-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_chrono-1.78.0\libboost_chrono-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\boost_date_time-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_date_time-1.78.0\libboost_date_time-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\boost_filesystem-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_filesystem-1.78.0\libboost_filesystem-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_headers-1.78.0\boost_headers-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\boost_iostreams-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_iostreams-1.78.0\libboost_iostreams-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\boost_program_options-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_program_options-1.78.0\libboost_program_options-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\boost_serialization-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_serialization-1.78.0\libboost_serialization-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\boost_system-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_system-1.78.0\libboost_system-variant-vc142-mt-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config-version.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\boost_thread-config.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-gd-x64-1_78-static.cmake;C:\opt\PCL\3rdParty\Boost\lib\cmake\boost_thread-1.78.0\libboost_thread-variant-vc142-mt-x64-1_78-static.cmake;D:\newFuxios\builder\cmake\add_boost.cmake;D:\newFuxios\builder\cmake\add_glog.cmake;D:\newFuxios\builder\cmake\add_libmodbus.cmake;D:\newFuxios\builder\cmake\common.cmake;D:\newFuxios\builder\cmake\library.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\newFuxios\build\Analysis_Robot\drivers\robotDriver\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\newFuxios\build\ZERO_CHECK.vcxproj">
      <Project>{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Analysis_Robot\drivers\robotDriver\Analysis_RobotdriversrobotDriver.vcxproj">
      <Project>{8FB30811-7331-3D2B-A90C-49741659A70F}</Project>
      <Name>Analysis_RobotdriversrobotDriver</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>