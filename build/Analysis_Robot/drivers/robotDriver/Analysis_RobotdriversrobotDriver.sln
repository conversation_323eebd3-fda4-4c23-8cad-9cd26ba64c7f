
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}"
	ProjectSection(ProjectDependencies) = postProject
		{8FB30811-7331-3D2B-A90C-49741659A70F} = {8FB30811-7331-3D2B-A90C-49741659A70F}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Analysis_RobotdriversrobotDriver", "Analysis_RobotdriversrobotDriver.vcxproj", "{8FB30811-7331-3D2B-A90C-49741659A70F}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{62C14041-4454-3A2C-A56A-D814CA14B33E}"
	ProjectSection(ProjectDependencies) = postProject
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1} = {7A1AD338-D22D-364E-8F8C-42805FEC1CD1}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\..\\ZERO_CHECK.vcxproj", "{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "fuxicommon", "..\..\..\fuxicommon\fuxicommon.vcxproj", "{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.Debug|x64.ActiveCfg = Debug|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.Debug|x64.Build.0 = Debug|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.Release|x64.ActiveCfg = Release|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.Release|x64.Build.0 = Release|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.Debug|x64.ActiveCfg = Debug|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.Debug|x64.Build.0 = Debug|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.Release|x64.ActiveCfg = Release|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.Release|x64.Build.0 = Release|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{62C14041-4454-3A2C-A56A-D814CA14B33E}.Debug|x64.ActiveCfg = Debug|x64
		{62C14041-4454-3A2C-A56A-D814CA14B33E}.Release|x64.ActiveCfg = Release|x64
		{62C14041-4454-3A2C-A56A-D814CA14B33E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{62C14041-4454-3A2C-A56A-D814CA14B33E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.Debug|x64.ActiveCfg = Debug|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.Debug|x64.Build.0 = Debug|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.Release|x64.ActiveCfg = Release|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.Release|x64.Build.0 = Release|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.Debug|x64.ActiveCfg = Debug|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.Debug|x64.Build.0 = Debug|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.Release|x64.ActiveCfg = Release|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.Release|x64.Build.0 = Release|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {5712928D-C98F-31AA-99E7-3F77D8FA8E08}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
