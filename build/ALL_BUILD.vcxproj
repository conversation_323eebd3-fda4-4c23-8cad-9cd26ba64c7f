<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\newFuxios\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/newFuxios/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/newFuxios -BD:/newFuxios/build --check-stamp-file D:/newFuxios/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake;D:\newFuxios\builder\cmake\ThirdPartyConfig.cmake;D:\newFuxios\builder\cmake\depends.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\newFuxios\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/newFuxios/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/newFuxios -BD:/newFuxios/build --check-stamp-file D:/newFuxios/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake;D:\newFuxios\builder\cmake\ThirdPartyConfig.cmake;D:\newFuxios\builder\cmake\depends.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\newFuxios\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/newFuxios/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/newFuxios -BD:/newFuxios/build --check-stamp-file D:/newFuxios/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake;D:\newFuxios\builder\cmake\ThirdPartyConfig.cmake;D:\newFuxios\builder\cmake\depends.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\newFuxios\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/newFuxios/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/newFuxios -BD:/newFuxios/build --check-stamp-file D:/newFuxios/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeCCompiler.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake;D:\newFuxios\build\CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake;D:\newFuxios\builder\cmake\ThirdPartyConfig.cmake;D:\newFuxios\builder\cmake\depends.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\newFuxios\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\newFuxios\build\ZERO_CHECK.vcxproj">
      <Project>{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Analysis_Robot\drivers\balanceDriver\Analysis_RobotdriversbalanceDriver.vcxproj">
      <Project>{B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0}</Project>
      <Name>Analysis_RobotdriversbalanceDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Analysis_Robot\drivers\heatingMagneticStirrerDriver\Analysis_RobotdriversheatingMagneticStirrerDriver.vcxproj">
      <Project>{07D278DE-7064-3024-94B7-53A1FEC8E5D3}</Project>
      <Name>Analysis_RobotdriversheatingMagneticStirrerDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Analysis_Robot\drivers\moistureAnalyzerDriver\Analysis_RobotdriversmoistureAnalyzerDriver.vcxproj">
      <Project>{61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA}</Project>
      <Name>Analysis_RobotdriversmoistureAnalyzerDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Analysis_Robot\drivers\restInterfaceDriver\Analysis_RobotdriversrestInterfaceDriver.vcxproj">
      <Project>{21C42F3C-91B9-3FC8-88F2-150F3A490633}</Project>
      <Name>Analysis_RobotdriversrestInterfaceDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Analysis_Robot\drivers\robotDriver\Analysis_RobotdriversrobotDriver.vcxproj">
      <Project>{8FB30811-7331-3D2B-A90C-49741659A70F}</Project>
      <Name>Analysis_RobotdriversrobotDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Analysis_Robot\test\balanceDriverTest\Analysis_RobottestbalanceDriverTest.vcxproj">
      <Project>{36EC82CE-C8B0-36C7-B6C2-DD3E46382734}</Project>
      <Name>Analysis_RobottestbalanceDriverTest</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Analysis_Robot\test\moistureAnalyzerDriverTest\Analysis_RobottestmoistureAnalyzerDriverTest.vcxproj">
      <Project>{C039F5AE-B10B-31DD-A3B3-2415C5C195D7}</Project>
      <Name>Analysis_RobottestmoistureAnalyzerDriverTest</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Analysis_Robot\test\restInterfaceDriverTest\Analysis_RobottestrestInterfaceDriverTest.vcxproj">
      <Project>{E0C0284E-F467-3FA2-8941-8290A29B3137}</Project>
      <Name>Analysis_RobottestrestInterfaceDriverTest</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\MJServer\APP\MJServerAPP.vcxproj">
      <Project>{2DF0F3C4-3C01-3C1C-8B1B-99898D3E52E8}</Project>
      <Name>MJServerAPP</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\MJServer_Refactor\App\MJServer_RefactorApp.vcxproj">
      <Project>{FD335FF3-16D9-33C8-A610-16FE18483BC8}</Project>
      <Name>MJServer_RefactorApp</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\MJServer_Refactor\Library\MJServer_RefactorLibrary.vcxproj">
      <Project>{C112EB0A-C45A-351A-898E-ECF83E60EAA9}</Project>
      <Name>MJServer_RefactorLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\MJServer_Refactor\Test\phase1_test\MJServer_RefactorTestphase1_test.vcxproj">
      <Project>{4B28A824-1F9C-3C6A-840E-2FF837A4AECB}</Project>
      <Name>MJServer_RefactorTestphase1_test</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\MJServer_Refactor\Test\simple_abb_client\MJServer_RefactorTestsimple_abb_client.vcxproj">
      <Project>{4FBA0801-83CC-361E-861F-E8D8FF493273}</Project>
      <Name>MJServer_RefactorTestsimple_abb_client</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\MJServer_Refactor\Test\simple_feeder_client\MJServer_RefactorTestsimple_feeder_client.vcxproj">
      <Project>{016D25C4-6276-3506-A3D6-3B433438CBCA}</Project>
      <Name>MJServer_RefactorTestsimple_feeder_client</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\RoboticLaserMarking\AbbDriver\RoboticLaserMarkingAbbDriver.vcxproj">
      <Project>{95F80C34-4C0F-3688-B8AD-324AFBE70658}</Project>
      <Name>RoboticLaserMarkingAbbDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\RoboticLaserMarking\LicenseGenerator\RoboticLaserMarkingLicenseGenerator.vcxproj">
      <Project>{32584D3A-7AF4-33C3-B406-FB26BFEA1B43}</Project>
      <Name>RoboticLaserMarkingLicenseGenerator</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\RoboticLaserMarking\RFIDDriver\RoboticLaserMarkingRFIDDriver.vcxproj">
      <Project>{73DCD364-1F26-3A84-8170-EDB484793676}</Project>
      <Name>RoboticLaserMarkingRFIDDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\RoboticLaserMarking\Test\abbsocket\RoboticLaserMarkingTestabbsocket.vcxproj">
      <Project>{31C9AD37-21B5-3F89-B851-2A54A072ECBF}</Project>
      <Name>RoboticLaserMarkingTestabbsocket</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\RoboticLaserMarking\Test\laser\RoboticLaserMarkingTestlaser.vcxproj">
      <Project>{1B29DC1E-6510-3232-B220-FBB305A263F3}</Project>
      <Name>RoboticLaserMarkingTestlaser</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\RoboticLaserMarking\Test\laserUI\RoboticLaserMarkingTestlaserUI.vcxproj">
      <Project>{42C038B7-3B6F-3570-89A2-701AF53C0423}</Project>
      <Name>RoboticLaserMarkingTestlaserUI</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\RoboticLaserMarking\Test\rfiddriver\RoboticLaserMarkingTestrfiddriver.vcxproj">
      <Project>{4677FA9F-9CE1-3F17-BB58-C3B5D77D9F6D}</Project>
      <Name>RoboticLaserMarkingTestrfiddriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\RoboticLaserMarking\Test\rfidserver\RoboticLaserMarkingTestrfidserver.vcxproj">
      <Project>{FA7363C8-2999-3120-861A-A8CF50C3A909}</Project>
      <Name>RoboticLaserMarkingTestrfidserver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\RoboticLaserMarking\UI\RoboticLaserMarkingUI.vcxproj">
      <Project>{F3359BEE-A66C-3509-8A6A-2E5A50D12C8E}</Project>
      <Name>RoboticLaserMarkingUI</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\RoboticLaserMarking\laserDriver\RoboticLaserMarkinglaserDriver.vcxproj">
      <Project>{17C53561-877C-3244-8907-DA5226D77C3A}</Project>
      <Name>RoboticLaserMarkinglaserDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\RoboticLaserMarking\laserDriverSim\RoboticLaserMarkinglaserDriverSim.vcxproj">
      <Project>{4E527281-D943-3E6C-BE56-D4F67F7B9498}</Project>
      <Name>RoboticLaserMarkinglaserDriverSim</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_abb_socket\Testtest_abb_socket.vcxproj">
      <Project>{FE8E8BC7-D1E6-34CA-B4FE-7E2659576F1C}</Project>
      <Name>Testtest_abb_socket</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_config_manager\Testtest_config_manager.vcxproj">
      <Project>{7113232F-148D-3F5D-B62C-DC76A1B51F2F}</Project>
      <Name>Testtest_config_manager</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_csv\Testtest_csv.vcxproj">
      <Project>{F6F1DE90-D2E8-324D-A5F4-3927A9F1F6B0}</Project>
      <Name>Testtest_csv</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_event_listener\Testtest_event_listener.vcxproj">
      <Project>{E78A8775-F3E5-309B-8A3A-3B486830BB62}</Project>
      <Name>Testtest_event_listener</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_executor\Testtest_executor.vcxproj">
      <Project>{ECF42B91-8CDB-375F-B8B7-6DE8CB4D1777}</Project>
      <Name>Testtest_executor</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_executor_context\Testtest_executor_context.vcxproj">
      <Project>{333AA83C-B582-3022-80BC-50B45ACA80BD}</Project>
      <Name>Testtest_executor_context</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_fileutil\Testtest_fileutil.vcxproj">
      <Project>{E28F6E83-6C82-3946-99AC-6CB03244A307}</Project>
      <Name>Testtest_fileutil</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_json\Testtest_json.vcxproj">
      <Project>{09D90722-D2FA-32A0-8074-6F830F927A67}</Project>
      <Name>Testtest_json</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_license_manager\Testtest_license_manager.vcxproj">
      <Project>{C129638B-4930-387A-9D7F-502158595676}</Project>
      <Name>Testtest_license_manager</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_license_ui\Testtest_license_ui.vcxproj">
      <Project>{3A807A57-8AC1-3338-985F-C9DA58AF63C6}</Project>
      <Name>Testtest_license_ui</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_network\Testtest_network.vcxproj">
      <Project>{4FBEDB73-D215-33DF-B27B-40BF0BC05ADB}</Project>
      <Name>Testtest_network</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_serial\Testtest_serial.vcxproj">
      <Project>{8E39805E-45BF-37AB-9D68-D46C8F4B9BC6}</Project>
      <Name>Testtest_serial</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_service_container\Testtest_service_container.vcxproj">
      <Project>{8FA4BFF9-BCD4-3BF3-A7B9-61CF8EF42B0A}</Project>
      <Name>Testtest_service_container</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_socket\Testtest_socket.vcxproj">
      <Project>{4A490540-92FB-3D63-8FA3-144DE4F42CBD}</Project>
      <Name>Testtest_socket</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_sqlite\Testtest_sqlite.vcxproj">
      <Project>{F963A301-5191-3348-82F2-C017D32D381E}</Project>
      <Name>Testtest_sqlite</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_taskflow\Testtest_taskflow.vcxproj">
      <Project>{5853A3B2-1393-372B-AE28-29C6E01E403A}</Project>
      <Name>Testtest_taskflow</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_twoaixsrobot\Testtest_twoaixsrobot.vcxproj">
      <Project>{A20B6C67-12DD-3D86-A5FF-4E6EF34CE82D}</Project>
      <Name>Testtest_twoaixsrobot</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\Test\test_xml\Testtest_xml.vcxproj">
      <Project>{4D2B3CDD-C9F7-3603-A9B3-B0E02654E57C}</Project>
      <Name>Testtest_xml</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\fuxicommon\fuxicommon.vcxproj">
      <Project>{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}</Project>
      <Name>fuxicommon</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\fuxicore\fuxicore.vcxproj">
      <Project>{2159201B-1B6E-364F-951F-3D7632A23AFC}</Project>
      <Name>fuxicore</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\AuboArcsDriver\hardwaredriverAuboArcsDriver.vcxproj">
      <Project>{55029551-E0D0-3EF7-8998-206701805306}</Project>
      <Name>hardwaredriverAuboArcsDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\AuboDriver\hardwaredriverAuboDriver.vcxproj">
      <Project>{CAB65C1F-1D6A-354E-9023-9E5A6222A979}</Project>
      <Name>hardwaredriverAuboDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\ElectricGripperDriver\hardwaredriverElectricGripperDriver.vcxproj">
      <Project>{95832015-F503-3E99-9279-AC3F77E5444B}</Project>
      <Name>hardwaredriverElectricGripperDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\HikVisionCamera\hardwaredriverHikVisionCamera.vcxproj">
      <Project>{492769B1-BC82-355C-B717-024749764B8A}</Project>
      <Name>hardwaredriverHikVisionCamera</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\LabelPrinter\hardwaredriverLabelPrinter.vcxproj">
      <Project>{207D94D9-DA8F-355A-BF59-A4C8E3CA14EF}</Project>
      <Name>hardwaredriverLabelPrinter</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\MettlerBalance\hardwaredriverMettlerBalance.vcxproj">
      <Project>{BAB254DC-65D8-3B0D-B5F4-3CCEDCEA355E}</Project>
      <Name>hardwaredriverMettlerBalance</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\OpcDa\hardwaredriverOpcDa.vcxproj">
      <Project>{839C181D-28DA-382E-A6D1-9C5F18411D1D}</Project>
      <Name>hardwaredriverOpcDa</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\OpcUa\hardwaredriverOpcUa.vcxproj">
      <Project>{FCFD7D21-2146-3CBF-83F0-10812F78D9B4}</Project>
      <Name>hardwaredriverOpcUa</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\abbRobotDriver\hardwaredriverabbRobotDriver.vcxproj">
      <Project>{9C6DA0B4-C233-32CF-8243-2A9CE50FFC99}</Project>
      <Name>hardwaredriverabbRobotDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\agilerobotDriver\hardwaredriveragilerobotDriver.vcxproj">
      <Project>{A998C219-A034-3AF9-BED5-DF7A7109E126}</Project>
      <Name>hardwaredriveragilerobotDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\fairinoDriver\hardwaredriverfairinoDriver.vcxproj">
      <Project>{7B028B37-669C-34D3-AC69-ADD0E748555C}</Project>
      <Name>hardwaredriverfairinoDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\junduoHandDriver\hardwaredriverjunduoHandDriver.vcxproj">
      <Project>{4C1F4BB3-E99B-39B3-A75E-D9FE17F0C595}</Project>
      <Name>hardwaredriverjunduoHandDriver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\modbus\hardwaredrivermodbus.vcxproj">
      <Project>{F2A19983-B04A-344B-88F1-BD19733FD5AF}</Project>
      <Name>hardwaredrivermodbus</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\serial\hardwaredriverserial.vcxproj">
      <Project>{FB77B135-8F5E-36AF-9136-D7F81F402B26}</Project>
      <Name>hardwaredriverserial</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\socket\hardwaredriversocket.vcxproj">
      <Project>{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}</Project>
      <Name>hardwaredriversocket</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\hardwaredriver\usbcamera\hardwaredriverusbcamera.vcxproj">
      <Project>{87EC0E93-8839-3F34-B1A2-D89CE2276646}</Project>
      <Name>hardwaredriverusbcamera</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\tool\calbuild\toolcalbuild.vcxproj">
      <Project>{610E614D-95B6-3CF5-983D-15B9DB6560E6}</Project>
      <Name>toolcalbuild</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\tool\caltest\toolcaltest.vcxproj">
      <Project>{169CEAE6-AC64-395A-A1A5-1773B9DE5FA3}</Project>
      <Name>toolcaltest</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\tool\cameraCalibrator\toolcameraCalibrator.vcxproj">
      <Project>{9BCA4545-8534-38B7-A366-D76C2A72E8FE}</Project>
      <Name>toolcameraCalibrator</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\tool\communication\toolcommunication.vcxproj">
      <Project>{4CE14F60-9B4A-3059-A868-C38F6759921E}</Project>
      <Name>toolcommunication</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\tool\handeyecal\toolhandeyecal.vcxproj">
      <Project>{D3979881-D853-3376-83BA-4C2E22043EEB}</Project>
      <Name>toolhandeyecal</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\tool\handeyecaltest\toolhandeyecaltest.vcxproj">
      <Project>{AD25EBA1-BE06-384A-8390-574D35B2CEA3}</Project>
      <Name>toolhandeyecaltest</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\tool\handeyecalui\handeyecalui\toolhandeyecaluihandeyecalui.vcxproj">
      <Project>{4F74D0DA-53E5-3C2C-B355-2D0BFDB16385}</Project>
      <Name>toolhandeyecaluihandeyecalui</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\tool\handeyecaluipath\toolhandeyecaluipath.vcxproj">
      <Project>{41D409D0-14D4-38E1-A74C-4DA2F02CF51B}</Project>
      <Name>toolhandeyecaluipath</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\tool\handeyecaluipathAuto\toolhandeyecaluipathAuto.vcxproj">
      <Project>{D0A34FE7-72E3-38B7-8DC7-519F82E46F38}</Project>
      <Name>toolhandeyecaluipathAuto</Name>
    </ProjectReference>
    <ProjectReference Include="D:\newFuxios\build\tool\verify_calibration\toolverify_calibration.vcxproj">
      <Project>{5902CDDD-E76B-3D9A-9181-B0EC767F81FB}</Project>
      <Name>toolverify_calibration</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>