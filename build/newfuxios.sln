
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}"
	ProjectSection(ProjectDependencies) = postProject
		{B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0} = {B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0}
		{07D278DE-7064-3024-94B7-53A1FEC8E5D3} = {07D278DE-7064-3024-94B7-53A1FEC8E5D3}
		{61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA} = {61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA}
		{21C42F3C-91B9-3FC8-88F2-150F3A490633} = {21C42F3C-91B9-3FC8-88F2-150F3A490633}
		{8FB30811-7331-3D2B-A90C-49741659A70F} = {8FB30811-7331-3D2B-A90C-49741659A70F}
		{36EC82CE-C8B0-36C7-B6C2-DD3E46382734} = {36EC82CE-C8B0-36C7-B6C2-DD3E46382734}
		{C039F5AE-B10B-31DD-A3B3-2415C5C195D7} = {C039F5AE-B10B-31DD-A3B3-2415C5C195D7}
		{E0C0284E-F467-3FA2-8941-8290A29B3137} = {E0C0284E-F467-3FA2-8941-8290A29B3137}
		{2DF0F3C4-3C01-3C1C-8B1B-99898D3E52E8} = {2DF0F3C4-3C01-3C1C-8B1B-99898D3E52E8}
		{FD335FF3-16D9-33C8-A610-16FE18483BC8} = {FD335FF3-16D9-33C8-A610-16FE18483BC8}
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9} = {C112EB0A-C45A-351A-898E-ECF83E60EAA9}
		{4B28A824-1F9C-3C6A-840E-2FF837A4AECB} = {4B28A824-1F9C-3C6A-840E-2FF837A4AECB}
		{4FBA0801-83CC-361E-861F-E8D8FF493273} = {4FBA0801-83CC-361E-861F-E8D8FF493273}
		{016D25C4-6276-3506-A3D6-3B433438CBCA} = {016D25C4-6276-3506-A3D6-3B433438CBCA}
		{95F80C34-4C0F-3688-B8AD-324AFBE70658} = {95F80C34-4C0F-3688-B8AD-324AFBE70658}
		{32584D3A-7AF4-33C3-B406-FB26BFEA1B43} = {32584D3A-7AF4-33C3-B406-FB26BFEA1B43}
		{73DCD364-1F26-3A84-8170-EDB484793676} = {73DCD364-1F26-3A84-8170-EDB484793676}
		{31C9AD37-21B5-3F89-B851-2A54A072ECBF} = {31C9AD37-21B5-3F89-B851-2A54A072ECBF}
		{1B29DC1E-6510-3232-B220-FBB305A263F3} = {1B29DC1E-6510-3232-B220-FBB305A263F3}
		{42C038B7-3B6F-3570-89A2-701AF53C0423} = {42C038B7-3B6F-3570-89A2-701AF53C0423}
		{4677FA9F-9CE1-3F17-BB58-C3B5D77D9F6D} = {4677FA9F-9CE1-3F17-BB58-C3B5D77D9F6D}
		{FA7363C8-2999-3120-861A-A8CF50C3A909} = {FA7363C8-2999-3120-861A-A8CF50C3A909}
		{F3359BEE-A66C-3509-8A6A-2E5A50D12C8E} = {F3359BEE-A66C-3509-8A6A-2E5A50D12C8E}
		{17C53561-877C-3244-8907-DA5226D77C3A} = {17C53561-877C-3244-8907-DA5226D77C3A}
		{4E527281-D943-3E6C-BE56-D4F67F7B9498} = {4E527281-D943-3E6C-BE56-D4F67F7B9498}
		{FE8E8BC7-D1E6-34CA-B4FE-7E2659576F1C} = {FE8E8BC7-D1E6-34CA-B4FE-7E2659576F1C}
		{7113232F-148D-3F5D-B62C-DC76A1B51F2F} = {7113232F-148D-3F5D-B62C-DC76A1B51F2F}
		{F6F1DE90-D2E8-324D-A5F4-3927A9F1F6B0} = {F6F1DE90-D2E8-324D-A5F4-3927A9F1F6B0}
		{E78A8775-F3E5-309B-8A3A-3B486830BB62} = {E78A8775-F3E5-309B-8A3A-3B486830BB62}
		{ECF42B91-8CDB-375F-B8B7-6DE8CB4D1777} = {ECF42B91-8CDB-375F-B8B7-6DE8CB4D1777}
		{333AA83C-B582-3022-80BC-50B45ACA80BD} = {333AA83C-B582-3022-80BC-50B45ACA80BD}
		{E28F6E83-6C82-3946-99AC-6CB03244A307} = {E28F6E83-6C82-3946-99AC-6CB03244A307}
		{09D90722-D2FA-32A0-8074-6F830F927A67} = {09D90722-D2FA-32A0-8074-6F830F927A67}
		{C129638B-4930-387A-9D7F-502158595676} = {C129638B-4930-387A-9D7F-502158595676}
		{3A807A57-8AC1-3338-985F-C9DA58AF63C6} = {3A807A57-8AC1-3338-985F-C9DA58AF63C6}
		{4FBEDB73-D215-33DF-B27B-40BF0BC05ADB} = {4FBEDB73-D215-33DF-B27B-40BF0BC05ADB}
		{8E39805E-45BF-37AB-9D68-D46C8F4B9BC6} = {8E39805E-45BF-37AB-9D68-D46C8F4B9BC6}
		{8FA4BFF9-BCD4-3BF3-A7B9-61CF8EF42B0A} = {8FA4BFF9-BCD4-3BF3-A7B9-61CF8EF42B0A}
		{4A490540-92FB-3D63-8FA3-144DE4F42CBD} = {4A490540-92FB-3D63-8FA3-144DE4F42CBD}
		{F963A301-5191-3348-82F2-C017D32D381E} = {F963A301-5191-3348-82F2-C017D32D381E}
		{5853A3B2-1393-372B-AE28-29C6E01E403A} = {5853A3B2-1393-372B-AE28-29C6E01E403A}
		{A20B6C67-12DD-3D86-A5FF-4E6EF34CE82D} = {A20B6C67-12DD-3D86-A5FF-4E6EF34CE82D}
		{4D2B3CDD-C9F7-3603-A9B3-B0E02654E57C} = {4D2B3CDD-C9F7-3603-A9B3-B0E02654E57C}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{2159201B-1B6E-364F-951F-3D7632A23AFC} = {2159201B-1B6E-364F-951F-3D7632A23AFC}
		{55029551-E0D0-3EF7-8998-206701805306} = {55029551-E0D0-3EF7-8998-206701805306}
		{CAB65C1F-1D6A-354E-9023-9E5A6222A979} = {CAB65C1F-1D6A-354E-9023-9E5A6222A979}
		{95832015-F503-3E99-9279-AC3F77E5444B} = {95832015-F503-3E99-9279-AC3F77E5444B}
		{492769B1-BC82-355C-B717-024749764B8A} = {492769B1-BC82-355C-B717-024749764B8A}
		{207D94D9-DA8F-355A-BF59-A4C8E3CA14EF} = {207D94D9-DA8F-355A-BF59-A4C8E3CA14EF}
		{BAB254DC-65D8-3B0D-B5F4-3CCEDCEA355E} = {BAB254DC-65D8-3B0D-B5F4-3CCEDCEA355E}
		{839C181D-28DA-382E-A6D1-9C5F18411D1D} = {839C181D-28DA-382E-A6D1-9C5F18411D1D}
		{FCFD7D21-2146-3CBF-83F0-10812F78D9B4} = {FCFD7D21-2146-3CBF-83F0-10812F78D9B4}
		{9C6DA0B4-C233-32CF-8243-2A9CE50FFC99} = {9C6DA0B4-C233-32CF-8243-2A9CE50FFC99}
		{A998C219-A034-3AF9-BED5-DF7A7109E126} = {A998C219-A034-3AF9-BED5-DF7A7109E126}
		{7B028B37-669C-34D3-AC69-ADD0E748555C} = {7B028B37-669C-34D3-AC69-ADD0E748555C}
		{4C1F4BB3-E99B-39B3-A75E-D9FE17F0C595} = {4C1F4BB3-E99B-39B3-A75E-D9FE17F0C595}
		{F2A19983-B04A-344B-88F1-BD19733FD5AF} = {F2A19983-B04A-344B-88F1-BD19733FD5AF}
		{FB77B135-8F5E-36AF-9136-D7F81F402B26} = {FB77B135-8F5E-36AF-9136-D7F81F402B26}
		{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F} = {F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}
		{87EC0E93-8839-3F34-B1A2-D89CE2276646} = {87EC0E93-8839-3F34-B1A2-D89CE2276646}
		{610E614D-95B6-3CF5-983D-15B9DB6560E6} = {610E614D-95B6-3CF5-983D-15B9DB6560E6}
		{169CEAE6-AC64-395A-A1A5-1773B9DE5FA3} = {169CEAE6-AC64-395A-A1A5-1773B9DE5FA3}
		{9BCA4545-8534-38B7-A366-D76C2A72E8FE} = {9BCA4545-8534-38B7-A366-D76C2A72E8FE}
		{4CE14F60-9B4A-3059-A868-C38F6759921E} = {4CE14F60-9B4A-3059-A868-C38F6759921E}
		{D3979881-D853-3376-83BA-4C2E22043EEB} = {D3979881-D853-3376-83BA-4C2E22043EEB}
		{AD25EBA1-BE06-384A-8390-574D35B2CEA3} = {AD25EBA1-BE06-384A-8390-574D35B2CEA3}
		{4F74D0DA-53E5-3C2C-B355-2D0BFDB16385} = {4F74D0DA-53E5-3C2C-B355-2D0BFDB16385}
		{41D409D0-14D4-38E1-A74C-4DA2F02CF51B} = {41D409D0-14D4-38E1-A74C-4DA2F02CF51B}
		{D0A34FE7-72E3-38B7-8DC7-519F82E46F38} = {D0A34FE7-72E3-38B7-8DC7-519F82E46F38}
		{5902CDDD-E76B-3D9A-9181-B0EC767F81FB} = {5902CDDD-E76B-3D9A-9181-B0EC767F81FB}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Analysis_RobotdriversbalanceDriver", "Analysis_Robot\drivers\balanceDriver\Analysis_RobotdriversbalanceDriver.vcxproj", "{B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Analysis_RobotdriversheatingMagneticStirrerDriver", "Analysis_Robot\drivers\heatingMagneticStirrerDriver\Analysis_RobotdriversheatingMagneticStirrerDriver.vcxproj", "{07D278DE-7064-3024-94B7-53A1FEC8E5D3}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Analysis_RobotdriversmoistureAnalyzerDriver", "Analysis_Robot\drivers\moistureAnalyzerDriver\Analysis_RobotdriversmoistureAnalyzerDriver.vcxproj", "{61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Analysis_RobotdriversrestInterfaceDriver", "Analysis_Robot\drivers\restInterfaceDriver\Analysis_RobotdriversrestInterfaceDriver.vcxproj", "{21C42F3C-91B9-3FC8-88F2-150F3A490633}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Analysis_RobotdriversrobotDriver", "Analysis_Robot\drivers\robotDriver\Analysis_RobotdriversrobotDriver.vcxproj", "{8FB30811-7331-3D2B-A90C-49741659A70F}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Analysis_RobottestbalanceDriverTest", "Analysis_Robot\test\balanceDriverTest\Analysis_RobottestbalanceDriverTest.vcxproj", "{36EC82CE-C8B0-36C7-B6C2-DD3E46382734}"
	ProjectSection(ProjectDependencies) = postProject
		{B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0} = {B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Analysis_RobottestmoistureAnalyzerDriverTest", "Analysis_Robot\test\moistureAnalyzerDriverTest\Analysis_RobottestmoistureAnalyzerDriverTest.vcxproj", "{C039F5AE-B10B-31DD-A3B3-2415C5C195D7}"
	ProjectSection(ProjectDependencies) = postProject
		{61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA} = {61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Analysis_RobottestrestInterfaceDriverTest", "Analysis_Robot\test\restInterfaceDriverTest\Analysis_RobottestrestInterfaceDriverTest.vcxproj", "{E0C0284E-F467-3FA2-8941-8290A29B3137}"
	ProjectSection(ProjectDependencies) = postProject
		{21C42F3C-91B9-3FC8-88F2-150F3A490633} = {21C42F3C-91B9-3FC8-88F2-150F3A490633}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{62C14041-4454-3A2C-A56A-D814CA14B33E}"
	ProjectSection(ProjectDependencies) = postProject
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1} = {7A1AD338-D22D-364E-8F8C-42805FEC1CD1}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "MJServerAPP", "MJServer\APP\MJServerAPP.vcxproj", "{2DF0F3C4-3C01-3C1C-8B1B-99898D3E52E8}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "MJServer_RefactorApp", "MJServer_Refactor\App\MJServer_RefactorApp.vcxproj", "{FD335FF3-16D9-33C8-A610-16FE18483BC8}"
	ProjectSection(ProjectDependencies) = postProject
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9} = {C112EB0A-C45A-351A-898E-ECF83E60EAA9}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "MJServer_RefactorLibrary", "MJServer_Refactor\Library\MJServer_RefactorLibrary.vcxproj", "{C112EB0A-C45A-351A-898E-ECF83E60EAA9}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "MJServer_RefactorTestphase1_test", "MJServer_Refactor\Test\phase1_test\MJServer_RefactorTestphase1_test.vcxproj", "{4B28A824-1F9C-3C6A-840E-2FF837A4AECB}"
	ProjectSection(ProjectDependencies) = postProject
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9} = {C112EB0A-C45A-351A-898E-ECF83E60EAA9}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "MJServer_RefactorTestsimple_abb_client", "MJServer_Refactor\Test\simple_abb_client\MJServer_RefactorTestsimple_abb_client.vcxproj", "{4FBA0801-83CC-361E-861F-E8D8FF493273}"
	ProjectSection(ProjectDependencies) = postProject
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9} = {C112EB0A-C45A-351A-898E-ECF83E60EAA9}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "MJServer_RefactorTestsimple_feeder_client", "MJServer_Refactor\Test\simple_feeder_client\MJServer_RefactorTestsimple_feeder_client.vcxproj", "{016D25C4-6276-3506-A3D6-3B433438CBCA}"
	ProjectSection(ProjectDependencies) = postProject
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9} = {C112EB0A-C45A-351A-898E-ECF83E60EAA9}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RoboticLaserMarkingAbbDriver", "RoboticLaserMarking\AbbDriver\RoboticLaserMarkingAbbDriver.vcxproj", "{95F80C34-4C0F-3688-B8AD-324AFBE70658}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RoboticLaserMarkingLicenseGenerator", "RoboticLaserMarking\LicenseGenerator\RoboticLaserMarkingLicenseGenerator.vcxproj", "{32584D3A-7AF4-33C3-B406-FB26BFEA1B43}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RoboticLaserMarkingRFIDDriver", "RoboticLaserMarking\RFIDDriver\RoboticLaserMarkingRFIDDriver.vcxproj", "{73DCD364-1F26-3A84-8170-EDB484793676}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RoboticLaserMarkingTestabbsocket", "RoboticLaserMarking\Test\abbsocket\RoboticLaserMarkingTestabbsocket.vcxproj", "{31C9AD37-21B5-3F89-B851-2A54A072ECBF}"
	ProjectSection(ProjectDependencies) = postProject
		{95F80C34-4C0F-3688-B8AD-324AFBE70658} = {95F80C34-4C0F-3688-B8AD-324AFBE70658}
		{73DCD364-1F26-3A84-8170-EDB484793676} = {73DCD364-1F26-3A84-8170-EDB484793676}
		{17C53561-877C-3244-8907-DA5226D77C3A} = {17C53561-877C-3244-8907-DA5226D77C3A}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RoboticLaserMarkingTestlaser", "RoboticLaserMarking\Test\laser\RoboticLaserMarkingTestlaser.vcxproj", "{1B29DC1E-6510-3232-B220-FBB305A263F3}"
	ProjectSection(ProjectDependencies) = postProject
		{17C53561-877C-3244-8907-DA5226D77C3A} = {17C53561-877C-3244-8907-DA5226D77C3A}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RoboticLaserMarkingTestlaserUI", "RoboticLaserMarking\Test\laserUI\RoboticLaserMarkingTestlaserUI.vcxproj", "{42C038B7-3B6F-3570-89A2-701AF53C0423}"
	ProjectSection(ProjectDependencies) = postProject
		{17C53561-877C-3244-8907-DA5226D77C3A} = {17C53561-877C-3244-8907-DA5226D77C3A}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RoboticLaserMarkingTestrfiddriver", "RoboticLaserMarking\Test\rfiddriver\RoboticLaserMarkingTestrfiddriver.vcxproj", "{4677FA9F-9CE1-3F17-BB58-C3B5D77D9F6D}"
	ProjectSection(ProjectDependencies) = postProject
		{73DCD364-1F26-3A84-8170-EDB484793676} = {73DCD364-1F26-3A84-8170-EDB484793676}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RoboticLaserMarkingTestrfidserver", "RoboticLaserMarking\Test\rfidserver\RoboticLaserMarkingTestrfidserver.vcxproj", "{FA7363C8-2999-3120-861A-A8CF50C3A909}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RoboticLaserMarkingUI", "RoboticLaserMarking\UI\RoboticLaserMarkingUI.vcxproj", "{F3359BEE-A66C-3509-8A6A-2E5A50D12C8E}"
	ProjectSection(ProjectDependencies) = postProject
		{95F80C34-4C0F-3688-B8AD-324AFBE70658} = {95F80C34-4C0F-3688-B8AD-324AFBE70658}
		{73DCD364-1F26-3A84-8170-EDB484793676} = {73DCD364-1F26-3A84-8170-EDB484793676}
		{17C53561-877C-3244-8907-DA5226D77C3A} = {17C53561-877C-3244-8907-DA5226D77C3A}
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RoboticLaserMarkinglaserDriver", "RoboticLaserMarking\laserDriver\RoboticLaserMarkinglaserDriver.vcxproj", "{17C53561-877C-3244-8907-DA5226D77C3A}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RoboticLaserMarkinglaserDriverSim", "RoboticLaserMarking\laserDriverSim\RoboticLaserMarkinglaserDriverSim.vcxproj", "{4E527281-D943-3E6C-BE56-D4F67F7B9498}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_abb_socket", "Test\test_abb_socket\Testtest_abb_socket.vcxproj", "{FE8E8BC7-D1E6-34CA-B4FE-7E2659576F1C}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{2159201B-1B6E-364F-951F-3D7632A23AFC} = {2159201B-1B6E-364F-951F-3D7632A23AFC}
		{9C6DA0B4-C233-32CF-8243-2A9CE50FFC99} = {9C6DA0B4-C233-32CF-8243-2A9CE50FFC99}
		{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F} = {F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_config_manager", "Test\test_config_manager\Testtest_config_manager.vcxproj", "{7113232F-148D-3F5D-B62C-DC76A1B51F2F}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{2159201B-1B6E-364F-951F-3D7632A23AFC} = {2159201B-1B6E-364F-951F-3D7632A23AFC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_csv", "Test\test_csv\Testtest_csv.vcxproj", "{F6F1DE90-D2E8-324D-A5F4-3927A9F1F6B0}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_event_listener", "Test\test_event_listener\Testtest_event_listener.vcxproj", "{E78A8775-F3E5-309B-8A3A-3B486830BB62}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_executor", "Test\test_executor\Testtest_executor.vcxproj", "{ECF42B91-8CDB-375F-B8B7-6DE8CB4D1777}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_executor_context", "Test\test_executor_context\Testtest_executor_context.vcxproj", "{333AA83C-B582-3022-80BC-50B45ACA80BD}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_fileutil", "Test\test_fileutil\Testtest_fileutil.vcxproj", "{E28F6E83-6C82-3946-99AC-6CB03244A307}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_json", "Test\test_json\Testtest_json.vcxproj", "{09D90722-D2FA-32A0-8074-6F830F927A67}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_license_manager", "Test\test_license_manager\Testtest_license_manager.vcxproj", "{C129638B-4930-387A-9D7F-502158595676}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{2159201B-1B6E-364F-951F-3D7632A23AFC} = {2159201B-1B6E-364F-951F-3D7632A23AFC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_license_ui", "Test\test_license_ui\Testtest_license_ui.vcxproj", "{3A807A57-8AC1-3338-985F-C9DA58AF63C6}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{2159201B-1B6E-364F-951F-3D7632A23AFC} = {2159201B-1B6E-364F-951F-3D7632A23AFC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_network", "Test\test_network\Testtest_network.vcxproj", "{4FBEDB73-D215-33DF-B27B-40BF0BC05ADB}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_serial", "Test\test_serial\Testtest_serial.vcxproj", "{8E39805E-45BF-37AB-9D68-D46C8F4B9BC6}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{2159201B-1B6E-364F-951F-3D7632A23AFC} = {2159201B-1B6E-364F-951F-3D7632A23AFC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_service_container", "Test\test_service_container\Testtest_service_container.vcxproj", "{8FA4BFF9-BCD4-3BF3-A7B9-61CF8EF42B0A}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{2159201B-1B6E-364F-951F-3D7632A23AFC} = {2159201B-1B6E-364F-951F-3D7632A23AFC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_socket", "Test\test_socket\Testtest_socket.vcxproj", "{4A490540-92FB-3D63-8FA3-144DE4F42CBD}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{2159201B-1B6E-364F-951F-3D7632A23AFC} = {2159201B-1B6E-364F-951F-3D7632A23AFC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_sqlite", "Test\test_sqlite\Testtest_sqlite.vcxproj", "{F963A301-5191-3348-82F2-C017D32D381E}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_taskflow", "Test\test_taskflow\Testtest_taskflow.vcxproj", "{5853A3B2-1393-372B-AE28-29C6E01E403A}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_twoaixsrobot", "Test\test_twoaixsrobot\Testtest_twoaixsrobot.vcxproj", "{A20B6C67-12DD-3D86-A5FF-4E6EF34CE82D}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{2159201B-1B6E-364F-951F-3D7632A23AFC} = {2159201B-1B6E-364F-951F-3D7632A23AFC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Testtest_xml", "Test\test_xml\Testtest_xml.vcxproj", "{4D2B3CDD-C9F7-3603-A9B3-B0E02654E57C}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "fuxicommon", "fuxicommon\fuxicommon.vcxproj", "{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "fuxicore", "fuxicore\fuxicore.vcxproj", "{2159201B-1B6E-364F-951F-3D7632A23AFC}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverAuboArcsDriver", "hardwaredriver\AuboArcsDriver\hardwaredriverAuboArcsDriver.vcxproj", "{55029551-E0D0-3EF7-8998-206701805306}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverAuboDriver", "hardwaredriver\AuboDriver\hardwaredriverAuboDriver.vcxproj", "{CAB65C1F-1D6A-354E-9023-9E5A6222A979}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverElectricGripperDriver", "hardwaredriver\ElectricGripperDriver\hardwaredriverElectricGripperDriver.vcxproj", "{95832015-F503-3E99-9279-AC3F77E5444B}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverHikVisionCamera", "hardwaredriver\HikVisionCamera\hardwaredriverHikVisionCamera.vcxproj", "{492769B1-BC82-355C-B717-024749764B8A}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverLabelPrinter", "hardwaredriver\LabelPrinter\hardwaredriverLabelPrinter.vcxproj", "{207D94D9-DA8F-355A-BF59-A4C8E3CA14EF}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverMettlerBalance", "hardwaredriver\MettlerBalance\hardwaredriverMettlerBalance.vcxproj", "{BAB254DC-65D8-3B0D-B5F4-3CCEDCEA355E}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverOpcDa", "hardwaredriver\OpcDa\hardwaredriverOpcDa.vcxproj", "{839C181D-28DA-382E-A6D1-9C5F18411D1D}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverOpcUa", "hardwaredriver\OpcUa\hardwaredriverOpcUa.vcxproj", "{FCFD7D21-2146-3CBF-83F0-10812F78D9B4}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverabbRobotDriver", "hardwaredriver\abbRobotDriver\hardwaredriverabbRobotDriver.vcxproj", "{9C6DA0B4-C233-32CF-8243-2A9CE50FFC99}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F} = {F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriveragilerobotDriver", "hardwaredriver\agilerobotDriver\hardwaredriveragilerobotDriver.vcxproj", "{A998C219-A034-3AF9-BED5-DF7A7109E126}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverfairinoDriver", "hardwaredriver\fairinoDriver\hardwaredriverfairinoDriver.vcxproj", "{7B028B37-669C-34D3-AC69-ADD0E748555C}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverjunduoHandDriver", "hardwaredriver\junduoHandDriver\hardwaredriverjunduoHandDriver.vcxproj", "{4C1F4BB3-E99B-39B3-A75E-D9FE17F0C595}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredrivermodbus", "hardwaredriver\modbus\hardwaredrivermodbus.vcxproj", "{F2A19983-B04A-344B-88F1-BD19733FD5AF}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverserial", "hardwaredriver\serial\hardwaredriverserial.vcxproj", "{FB77B135-8F5E-36AF-9136-D7F81F402B26}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriversocket", "hardwaredriver\socket\hardwaredriversocket.vcxproj", "{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "hardwaredriverusbcamera", "hardwaredriver\usbcamera\hardwaredriverusbcamera.vcxproj", "{87EC0E93-8839-3F34-B1A2-D89CE2276646}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "toolcalbuild", "tool\calbuild\toolcalbuild.vcxproj", "{610E614D-95B6-3CF5-983D-15B9DB6560E6}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "toolcaltest", "tool\caltest\toolcaltest.vcxproj", "{169CEAE6-AC64-395A-A1A5-1773B9DE5FA3}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{9BCA4545-8534-38B7-A366-D76C2A72E8FE} = {9BCA4545-8534-38B7-A366-D76C2A72E8FE}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "toolcameraCalibrator", "tool\cameraCalibrator\toolcameraCalibrator.vcxproj", "{9BCA4545-8534-38B7-A366-D76C2A72E8FE}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "toolcommunication", "tool\communication\toolcommunication.vcxproj", "{4CE14F60-9B4A-3059-A868-C38F6759921E}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "toolhandeyecal", "tool\handeyecal\toolhandeyecal.vcxproj", "{D3979881-D853-3376-83BA-4C2E22043EEB}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{55029551-E0D0-3EF7-8998-206701805306} = {55029551-E0D0-3EF7-8998-206701805306}
		{492769B1-BC82-355C-B717-024749764B8A} = {492769B1-BC82-355C-B717-024749764B8A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "toolhandeyecaltest", "tool\handeyecaltest\toolhandeyecaltest.vcxproj", "{AD25EBA1-BE06-384A-8390-574D35B2CEA3}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{55029551-E0D0-3EF7-8998-206701805306} = {55029551-E0D0-3EF7-8998-206701805306}
		{492769B1-BC82-355C-B717-024749764B8A} = {492769B1-BC82-355C-B717-024749764B8A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "toolhandeyecaluihandeyecalui", "tool\handeyecalui\handeyecalui\toolhandeyecaluihandeyecalui.vcxproj", "{4F74D0DA-53E5-3C2C-B355-2D0BFDB16385}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{55029551-E0D0-3EF7-8998-206701805306} = {55029551-E0D0-3EF7-8998-206701805306}
		{492769B1-BC82-355C-B717-024749764B8A} = {492769B1-BC82-355C-B717-024749764B8A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "toolhandeyecaluipath", "tool\handeyecaluipath\toolhandeyecaluipath.vcxproj", "{41D409D0-14D4-38E1-A74C-4DA2F02CF51B}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{55029551-E0D0-3EF7-8998-206701805306} = {55029551-E0D0-3EF7-8998-206701805306}
		{492769B1-BC82-355C-B717-024749764B8A} = {492769B1-BC82-355C-B717-024749764B8A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "toolhandeyecaluipathAuto", "tool\handeyecaluipathAuto\toolhandeyecaluipathAuto.vcxproj", "{D0A34FE7-72E3-38B7-8DC7-519F82E46F38}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
		{55029551-E0D0-3EF7-8998-206701805306} = {55029551-E0D0-3EF7-8998-206701805306}
		{492769B1-BC82-355C-B717-024749764B8A} = {492769B1-BC82-355C-B717-024749764B8A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "toolverify_calibration", "tool\verify_calibration\toolverify_calibration.vcxproj", "{5902CDDD-E76B-3D9A-9181-B0EC767F81FB}"
	ProjectSection(ProjectDependencies) = postProject
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8} = {46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861} = {60CF3C55-CC5F-3006-A5A5-F06D52DBE861}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.Debug|x64.ActiveCfg = Debug|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.Debug|x64.Build.0 = Debug|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.Release|x64.ActiveCfg = Release|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.Release|x64.Build.0 = Release|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7A1AD338-D22D-364E-8F8C-42805FEC1CD1}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0}.Debug|x64.ActiveCfg = Debug|x64
		{B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0}.Debug|x64.Build.0 = Debug|x64
		{B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0}.Release|x64.ActiveCfg = Release|x64
		{B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0}.Release|x64.Build.0 = Release|x64
		{B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B67BE2EE-4BD8-39F6-811D-E28F9E15DDF0}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{07D278DE-7064-3024-94B7-53A1FEC8E5D3}.Debug|x64.ActiveCfg = Debug|x64
		{07D278DE-7064-3024-94B7-53A1FEC8E5D3}.Debug|x64.Build.0 = Debug|x64
		{07D278DE-7064-3024-94B7-53A1FEC8E5D3}.Release|x64.ActiveCfg = Release|x64
		{07D278DE-7064-3024-94B7-53A1FEC8E5D3}.Release|x64.Build.0 = Release|x64
		{07D278DE-7064-3024-94B7-53A1FEC8E5D3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{07D278DE-7064-3024-94B7-53A1FEC8E5D3}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{07D278DE-7064-3024-94B7-53A1FEC8E5D3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{07D278DE-7064-3024-94B7-53A1FEC8E5D3}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA}.Debug|x64.ActiveCfg = Debug|x64
		{61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA}.Debug|x64.Build.0 = Debug|x64
		{61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA}.Release|x64.ActiveCfg = Release|x64
		{61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA}.Release|x64.Build.0 = Release|x64
		{61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{61D8778F-2EDA-38B2-BFE1-3C5241F2C4FA}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{21C42F3C-91B9-3FC8-88F2-150F3A490633}.Debug|x64.ActiveCfg = Debug|x64
		{21C42F3C-91B9-3FC8-88F2-150F3A490633}.Debug|x64.Build.0 = Debug|x64
		{21C42F3C-91B9-3FC8-88F2-150F3A490633}.Release|x64.ActiveCfg = Release|x64
		{21C42F3C-91B9-3FC8-88F2-150F3A490633}.Release|x64.Build.0 = Release|x64
		{21C42F3C-91B9-3FC8-88F2-150F3A490633}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{21C42F3C-91B9-3FC8-88F2-150F3A490633}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{21C42F3C-91B9-3FC8-88F2-150F3A490633}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{21C42F3C-91B9-3FC8-88F2-150F3A490633}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.Debug|x64.ActiveCfg = Debug|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.Debug|x64.Build.0 = Debug|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.Release|x64.ActiveCfg = Release|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.Release|x64.Build.0 = Release|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8FB30811-7331-3D2B-A90C-49741659A70F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{36EC82CE-C8B0-36C7-B6C2-DD3E46382734}.Debug|x64.ActiveCfg = Debug|x64
		{36EC82CE-C8B0-36C7-B6C2-DD3E46382734}.Debug|x64.Build.0 = Debug|x64
		{36EC82CE-C8B0-36C7-B6C2-DD3E46382734}.Release|x64.ActiveCfg = Release|x64
		{36EC82CE-C8B0-36C7-B6C2-DD3E46382734}.Release|x64.Build.0 = Release|x64
		{36EC82CE-C8B0-36C7-B6C2-DD3E46382734}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{36EC82CE-C8B0-36C7-B6C2-DD3E46382734}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{36EC82CE-C8B0-36C7-B6C2-DD3E46382734}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{36EC82CE-C8B0-36C7-B6C2-DD3E46382734}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{C039F5AE-B10B-31DD-A3B3-2415C5C195D7}.Debug|x64.ActiveCfg = Debug|x64
		{C039F5AE-B10B-31DD-A3B3-2415C5C195D7}.Debug|x64.Build.0 = Debug|x64
		{C039F5AE-B10B-31DD-A3B3-2415C5C195D7}.Release|x64.ActiveCfg = Release|x64
		{C039F5AE-B10B-31DD-A3B3-2415C5C195D7}.Release|x64.Build.0 = Release|x64
		{C039F5AE-B10B-31DD-A3B3-2415C5C195D7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C039F5AE-B10B-31DD-A3B3-2415C5C195D7}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C039F5AE-B10B-31DD-A3B3-2415C5C195D7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C039F5AE-B10B-31DD-A3B3-2415C5C195D7}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{E0C0284E-F467-3FA2-8941-8290A29B3137}.Debug|x64.ActiveCfg = Debug|x64
		{E0C0284E-F467-3FA2-8941-8290A29B3137}.Debug|x64.Build.0 = Debug|x64
		{E0C0284E-F467-3FA2-8941-8290A29B3137}.Release|x64.ActiveCfg = Release|x64
		{E0C0284E-F467-3FA2-8941-8290A29B3137}.Release|x64.Build.0 = Release|x64
		{E0C0284E-F467-3FA2-8941-8290A29B3137}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E0C0284E-F467-3FA2-8941-8290A29B3137}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E0C0284E-F467-3FA2-8941-8290A29B3137}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E0C0284E-F467-3FA2-8941-8290A29B3137}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{62C14041-4454-3A2C-A56A-D814CA14B33E}.Debug|x64.ActiveCfg = Debug|x64
		{62C14041-4454-3A2C-A56A-D814CA14B33E}.Release|x64.ActiveCfg = Release|x64
		{62C14041-4454-3A2C-A56A-D814CA14B33E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{62C14041-4454-3A2C-A56A-D814CA14B33E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2DF0F3C4-3C01-3C1C-8B1B-99898D3E52E8}.Debug|x64.ActiveCfg = Debug|x64
		{2DF0F3C4-3C01-3C1C-8B1B-99898D3E52E8}.Debug|x64.Build.0 = Debug|x64
		{2DF0F3C4-3C01-3C1C-8B1B-99898D3E52E8}.Release|x64.ActiveCfg = Release|x64
		{2DF0F3C4-3C01-3C1C-8B1B-99898D3E52E8}.Release|x64.Build.0 = Release|x64
		{2DF0F3C4-3C01-3C1C-8B1B-99898D3E52E8}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2DF0F3C4-3C01-3C1C-8B1B-99898D3E52E8}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{2DF0F3C4-3C01-3C1C-8B1B-99898D3E52E8}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2DF0F3C4-3C01-3C1C-8B1B-99898D3E52E8}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{FD335FF3-16D9-33C8-A610-16FE18483BC8}.Debug|x64.ActiveCfg = Debug|x64
		{FD335FF3-16D9-33C8-A610-16FE18483BC8}.Debug|x64.Build.0 = Debug|x64
		{FD335FF3-16D9-33C8-A610-16FE18483BC8}.Release|x64.ActiveCfg = Release|x64
		{FD335FF3-16D9-33C8-A610-16FE18483BC8}.Release|x64.Build.0 = Release|x64
		{FD335FF3-16D9-33C8-A610-16FE18483BC8}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FD335FF3-16D9-33C8-A610-16FE18483BC8}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{FD335FF3-16D9-33C8-A610-16FE18483BC8}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FD335FF3-16D9-33C8-A610-16FE18483BC8}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9}.Debug|x64.ActiveCfg = Debug|x64
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9}.Debug|x64.Build.0 = Debug|x64
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9}.Release|x64.ActiveCfg = Release|x64
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9}.Release|x64.Build.0 = Release|x64
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C112EB0A-C45A-351A-898E-ECF83E60EAA9}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4B28A824-1F9C-3C6A-840E-2FF837A4AECB}.Debug|x64.ActiveCfg = Debug|x64
		{4B28A824-1F9C-3C6A-840E-2FF837A4AECB}.Debug|x64.Build.0 = Debug|x64
		{4B28A824-1F9C-3C6A-840E-2FF837A4AECB}.Release|x64.ActiveCfg = Release|x64
		{4B28A824-1F9C-3C6A-840E-2FF837A4AECB}.Release|x64.Build.0 = Release|x64
		{4B28A824-1F9C-3C6A-840E-2FF837A4AECB}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4B28A824-1F9C-3C6A-840E-2FF837A4AECB}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4B28A824-1F9C-3C6A-840E-2FF837A4AECB}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4B28A824-1F9C-3C6A-840E-2FF837A4AECB}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4FBA0801-83CC-361E-861F-E8D8FF493273}.Debug|x64.ActiveCfg = Debug|x64
		{4FBA0801-83CC-361E-861F-E8D8FF493273}.Debug|x64.Build.0 = Debug|x64
		{4FBA0801-83CC-361E-861F-E8D8FF493273}.Release|x64.ActiveCfg = Release|x64
		{4FBA0801-83CC-361E-861F-E8D8FF493273}.Release|x64.Build.0 = Release|x64
		{4FBA0801-83CC-361E-861F-E8D8FF493273}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4FBA0801-83CC-361E-861F-E8D8FF493273}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4FBA0801-83CC-361E-861F-E8D8FF493273}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4FBA0801-83CC-361E-861F-E8D8FF493273}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{016D25C4-6276-3506-A3D6-3B433438CBCA}.Debug|x64.ActiveCfg = Debug|x64
		{016D25C4-6276-3506-A3D6-3B433438CBCA}.Debug|x64.Build.0 = Debug|x64
		{016D25C4-6276-3506-A3D6-3B433438CBCA}.Release|x64.ActiveCfg = Release|x64
		{016D25C4-6276-3506-A3D6-3B433438CBCA}.Release|x64.Build.0 = Release|x64
		{016D25C4-6276-3506-A3D6-3B433438CBCA}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{016D25C4-6276-3506-A3D6-3B433438CBCA}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{016D25C4-6276-3506-A3D6-3B433438CBCA}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{016D25C4-6276-3506-A3D6-3B433438CBCA}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{95F80C34-4C0F-3688-B8AD-324AFBE70658}.Debug|x64.ActiveCfg = Debug|x64
		{95F80C34-4C0F-3688-B8AD-324AFBE70658}.Debug|x64.Build.0 = Debug|x64
		{95F80C34-4C0F-3688-B8AD-324AFBE70658}.Release|x64.ActiveCfg = Release|x64
		{95F80C34-4C0F-3688-B8AD-324AFBE70658}.Release|x64.Build.0 = Release|x64
		{95F80C34-4C0F-3688-B8AD-324AFBE70658}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{95F80C34-4C0F-3688-B8AD-324AFBE70658}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{95F80C34-4C0F-3688-B8AD-324AFBE70658}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{95F80C34-4C0F-3688-B8AD-324AFBE70658}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{32584D3A-7AF4-33C3-B406-FB26BFEA1B43}.Debug|x64.ActiveCfg = Debug|x64
		{32584D3A-7AF4-33C3-B406-FB26BFEA1B43}.Debug|x64.Build.0 = Debug|x64
		{32584D3A-7AF4-33C3-B406-FB26BFEA1B43}.Release|x64.ActiveCfg = Release|x64
		{32584D3A-7AF4-33C3-B406-FB26BFEA1B43}.Release|x64.Build.0 = Release|x64
		{32584D3A-7AF4-33C3-B406-FB26BFEA1B43}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{32584D3A-7AF4-33C3-B406-FB26BFEA1B43}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{32584D3A-7AF4-33C3-B406-FB26BFEA1B43}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{32584D3A-7AF4-33C3-B406-FB26BFEA1B43}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{73DCD364-1F26-3A84-8170-EDB484793676}.Debug|x64.ActiveCfg = Debug|x64
		{73DCD364-1F26-3A84-8170-EDB484793676}.Debug|x64.Build.0 = Debug|x64
		{73DCD364-1F26-3A84-8170-EDB484793676}.Release|x64.ActiveCfg = Release|x64
		{73DCD364-1F26-3A84-8170-EDB484793676}.Release|x64.Build.0 = Release|x64
		{73DCD364-1F26-3A84-8170-EDB484793676}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{73DCD364-1F26-3A84-8170-EDB484793676}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{73DCD364-1F26-3A84-8170-EDB484793676}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{73DCD364-1F26-3A84-8170-EDB484793676}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{31C9AD37-21B5-3F89-B851-2A54A072ECBF}.Debug|x64.ActiveCfg = Debug|x64
		{31C9AD37-21B5-3F89-B851-2A54A072ECBF}.Debug|x64.Build.0 = Debug|x64
		{31C9AD37-21B5-3F89-B851-2A54A072ECBF}.Release|x64.ActiveCfg = Release|x64
		{31C9AD37-21B5-3F89-B851-2A54A072ECBF}.Release|x64.Build.0 = Release|x64
		{31C9AD37-21B5-3F89-B851-2A54A072ECBF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{31C9AD37-21B5-3F89-B851-2A54A072ECBF}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{31C9AD37-21B5-3F89-B851-2A54A072ECBF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{31C9AD37-21B5-3F89-B851-2A54A072ECBF}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{1B29DC1E-6510-3232-B220-FBB305A263F3}.Debug|x64.ActiveCfg = Debug|x64
		{1B29DC1E-6510-3232-B220-FBB305A263F3}.Debug|x64.Build.0 = Debug|x64
		{1B29DC1E-6510-3232-B220-FBB305A263F3}.Release|x64.ActiveCfg = Release|x64
		{1B29DC1E-6510-3232-B220-FBB305A263F3}.Release|x64.Build.0 = Release|x64
		{1B29DC1E-6510-3232-B220-FBB305A263F3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{1B29DC1E-6510-3232-B220-FBB305A263F3}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{1B29DC1E-6510-3232-B220-FBB305A263F3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{1B29DC1E-6510-3232-B220-FBB305A263F3}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{42C038B7-3B6F-3570-89A2-701AF53C0423}.Debug|x64.ActiveCfg = Debug|x64
		{42C038B7-3B6F-3570-89A2-701AF53C0423}.Debug|x64.Build.0 = Debug|x64
		{42C038B7-3B6F-3570-89A2-701AF53C0423}.Release|x64.ActiveCfg = Release|x64
		{42C038B7-3B6F-3570-89A2-701AF53C0423}.Release|x64.Build.0 = Release|x64
		{42C038B7-3B6F-3570-89A2-701AF53C0423}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{42C038B7-3B6F-3570-89A2-701AF53C0423}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{42C038B7-3B6F-3570-89A2-701AF53C0423}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{42C038B7-3B6F-3570-89A2-701AF53C0423}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4677FA9F-9CE1-3F17-BB58-C3B5D77D9F6D}.Debug|x64.ActiveCfg = Debug|x64
		{4677FA9F-9CE1-3F17-BB58-C3B5D77D9F6D}.Debug|x64.Build.0 = Debug|x64
		{4677FA9F-9CE1-3F17-BB58-C3B5D77D9F6D}.Release|x64.ActiveCfg = Release|x64
		{4677FA9F-9CE1-3F17-BB58-C3B5D77D9F6D}.Release|x64.Build.0 = Release|x64
		{4677FA9F-9CE1-3F17-BB58-C3B5D77D9F6D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4677FA9F-9CE1-3F17-BB58-C3B5D77D9F6D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4677FA9F-9CE1-3F17-BB58-C3B5D77D9F6D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4677FA9F-9CE1-3F17-BB58-C3B5D77D9F6D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{FA7363C8-2999-3120-861A-A8CF50C3A909}.Debug|x64.ActiveCfg = Debug|x64
		{FA7363C8-2999-3120-861A-A8CF50C3A909}.Debug|x64.Build.0 = Debug|x64
		{FA7363C8-2999-3120-861A-A8CF50C3A909}.Release|x64.ActiveCfg = Release|x64
		{FA7363C8-2999-3120-861A-A8CF50C3A909}.Release|x64.Build.0 = Release|x64
		{FA7363C8-2999-3120-861A-A8CF50C3A909}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FA7363C8-2999-3120-861A-A8CF50C3A909}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{FA7363C8-2999-3120-861A-A8CF50C3A909}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FA7363C8-2999-3120-861A-A8CF50C3A909}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F3359BEE-A66C-3509-8A6A-2E5A50D12C8E}.Debug|x64.ActiveCfg = Debug|x64
		{F3359BEE-A66C-3509-8A6A-2E5A50D12C8E}.Debug|x64.Build.0 = Debug|x64
		{F3359BEE-A66C-3509-8A6A-2E5A50D12C8E}.Release|x64.ActiveCfg = Release|x64
		{F3359BEE-A66C-3509-8A6A-2E5A50D12C8E}.Release|x64.Build.0 = Release|x64
		{F3359BEE-A66C-3509-8A6A-2E5A50D12C8E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F3359BEE-A66C-3509-8A6A-2E5A50D12C8E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F3359BEE-A66C-3509-8A6A-2E5A50D12C8E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F3359BEE-A66C-3509-8A6A-2E5A50D12C8E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{17C53561-877C-3244-8907-DA5226D77C3A}.Debug|x64.ActiveCfg = Debug|x64
		{17C53561-877C-3244-8907-DA5226D77C3A}.Debug|x64.Build.0 = Debug|x64
		{17C53561-877C-3244-8907-DA5226D77C3A}.Release|x64.ActiveCfg = Release|x64
		{17C53561-877C-3244-8907-DA5226D77C3A}.Release|x64.Build.0 = Release|x64
		{17C53561-877C-3244-8907-DA5226D77C3A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{17C53561-877C-3244-8907-DA5226D77C3A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{17C53561-877C-3244-8907-DA5226D77C3A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{17C53561-877C-3244-8907-DA5226D77C3A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4E527281-D943-3E6C-BE56-D4F67F7B9498}.Debug|x64.ActiveCfg = Debug|x64
		{4E527281-D943-3E6C-BE56-D4F67F7B9498}.Debug|x64.Build.0 = Debug|x64
		{4E527281-D943-3E6C-BE56-D4F67F7B9498}.Release|x64.ActiveCfg = Release|x64
		{4E527281-D943-3E6C-BE56-D4F67F7B9498}.Release|x64.Build.0 = Release|x64
		{4E527281-D943-3E6C-BE56-D4F67F7B9498}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4E527281-D943-3E6C-BE56-D4F67F7B9498}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4E527281-D943-3E6C-BE56-D4F67F7B9498}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4E527281-D943-3E6C-BE56-D4F67F7B9498}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{FE8E8BC7-D1E6-34CA-B4FE-7E2659576F1C}.Debug|x64.ActiveCfg = Debug|x64
		{FE8E8BC7-D1E6-34CA-B4FE-7E2659576F1C}.Debug|x64.Build.0 = Debug|x64
		{FE8E8BC7-D1E6-34CA-B4FE-7E2659576F1C}.Release|x64.ActiveCfg = Release|x64
		{FE8E8BC7-D1E6-34CA-B4FE-7E2659576F1C}.Release|x64.Build.0 = Release|x64
		{FE8E8BC7-D1E6-34CA-B4FE-7E2659576F1C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FE8E8BC7-D1E6-34CA-B4FE-7E2659576F1C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{FE8E8BC7-D1E6-34CA-B4FE-7E2659576F1C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FE8E8BC7-D1E6-34CA-B4FE-7E2659576F1C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{7113232F-148D-3F5D-B62C-DC76A1B51F2F}.Debug|x64.ActiveCfg = Debug|x64
		{7113232F-148D-3F5D-B62C-DC76A1B51F2F}.Debug|x64.Build.0 = Debug|x64
		{7113232F-148D-3F5D-B62C-DC76A1B51F2F}.Release|x64.ActiveCfg = Release|x64
		{7113232F-148D-3F5D-B62C-DC76A1B51F2F}.Release|x64.Build.0 = Release|x64
		{7113232F-148D-3F5D-B62C-DC76A1B51F2F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7113232F-148D-3F5D-B62C-DC76A1B51F2F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7113232F-148D-3F5D-B62C-DC76A1B51F2F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7113232F-148D-3F5D-B62C-DC76A1B51F2F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F6F1DE90-D2E8-324D-A5F4-3927A9F1F6B0}.Debug|x64.ActiveCfg = Debug|x64
		{F6F1DE90-D2E8-324D-A5F4-3927A9F1F6B0}.Debug|x64.Build.0 = Debug|x64
		{F6F1DE90-D2E8-324D-A5F4-3927A9F1F6B0}.Release|x64.ActiveCfg = Release|x64
		{F6F1DE90-D2E8-324D-A5F4-3927A9F1F6B0}.Release|x64.Build.0 = Release|x64
		{F6F1DE90-D2E8-324D-A5F4-3927A9F1F6B0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F6F1DE90-D2E8-324D-A5F4-3927A9F1F6B0}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F6F1DE90-D2E8-324D-A5F4-3927A9F1F6B0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F6F1DE90-D2E8-324D-A5F4-3927A9F1F6B0}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{E78A8775-F3E5-309B-8A3A-3B486830BB62}.Debug|x64.ActiveCfg = Debug|x64
		{E78A8775-F3E5-309B-8A3A-3B486830BB62}.Debug|x64.Build.0 = Debug|x64
		{E78A8775-F3E5-309B-8A3A-3B486830BB62}.Release|x64.ActiveCfg = Release|x64
		{E78A8775-F3E5-309B-8A3A-3B486830BB62}.Release|x64.Build.0 = Release|x64
		{E78A8775-F3E5-309B-8A3A-3B486830BB62}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E78A8775-F3E5-309B-8A3A-3B486830BB62}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E78A8775-F3E5-309B-8A3A-3B486830BB62}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E78A8775-F3E5-309B-8A3A-3B486830BB62}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{ECF42B91-8CDB-375F-B8B7-6DE8CB4D1777}.Debug|x64.ActiveCfg = Debug|x64
		{ECF42B91-8CDB-375F-B8B7-6DE8CB4D1777}.Debug|x64.Build.0 = Debug|x64
		{ECF42B91-8CDB-375F-B8B7-6DE8CB4D1777}.Release|x64.ActiveCfg = Release|x64
		{ECF42B91-8CDB-375F-B8B7-6DE8CB4D1777}.Release|x64.Build.0 = Release|x64
		{ECF42B91-8CDB-375F-B8B7-6DE8CB4D1777}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{ECF42B91-8CDB-375F-B8B7-6DE8CB4D1777}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{ECF42B91-8CDB-375F-B8B7-6DE8CB4D1777}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{ECF42B91-8CDB-375F-B8B7-6DE8CB4D1777}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{333AA83C-B582-3022-80BC-50B45ACA80BD}.Debug|x64.ActiveCfg = Debug|x64
		{333AA83C-B582-3022-80BC-50B45ACA80BD}.Debug|x64.Build.0 = Debug|x64
		{333AA83C-B582-3022-80BC-50B45ACA80BD}.Release|x64.ActiveCfg = Release|x64
		{333AA83C-B582-3022-80BC-50B45ACA80BD}.Release|x64.Build.0 = Release|x64
		{333AA83C-B582-3022-80BC-50B45ACA80BD}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{333AA83C-B582-3022-80BC-50B45ACA80BD}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{333AA83C-B582-3022-80BC-50B45ACA80BD}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{333AA83C-B582-3022-80BC-50B45ACA80BD}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{E28F6E83-6C82-3946-99AC-6CB03244A307}.Debug|x64.ActiveCfg = Debug|x64
		{E28F6E83-6C82-3946-99AC-6CB03244A307}.Debug|x64.Build.0 = Debug|x64
		{E28F6E83-6C82-3946-99AC-6CB03244A307}.Release|x64.ActiveCfg = Release|x64
		{E28F6E83-6C82-3946-99AC-6CB03244A307}.Release|x64.Build.0 = Release|x64
		{E28F6E83-6C82-3946-99AC-6CB03244A307}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E28F6E83-6C82-3946-99AC-6CB03244A307}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E28F6E83-6C82-3946-99AC-6CB03244A307}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E28F6E83-6C82-3946-99AC-6CB03244A307}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{09D90722-D2FA-32A0-8074-6F830F927A67}.Debug|x64.ActiveCfg = Debug|x64
		{09D90722-D2FA-32A0-8074-6F830F927A67}.Debug|x64.Build.0 = Debug|x64
		{09D90722-D2FA-32A0-8074-6F830F927A67}.Release|x64.ActiveCfg = Release|x64
		{09D90722-D2FA-32A0-8074-6F830F927A67}.Release|x64.Build.0 = Release|x64
		{09D90722-D2FA-32A0-8074-6F830F927A67}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{09D90722-D2FA-32A0-8074-6F830F927A67}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{09D90722-D2FA-32A0-8074-6F830F927A67}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{09D90722-D2FA-32A0-8074-6F830F927A67}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{C129638B-4930-387A-9D7F-502158595676}.Debug|x64.ActiveCfg = Debug|x64
		{C129638B-4930-387A-9D7F-502158595676}.Debug|x64.Build.0 = Debug|x64
		{C129638B-4930-387A-9D7F-502158595676}.Release|x64.ActiveCfg = Release|x64
		{C129638B-4930-387A-9D7F-502158595676}.Release|x64.Build.0 = Release|x64
		{C129638B-4930-387A-9D7F-502158595676}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C129638B-4930-387A-9D7F-502158595676}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C129638B-4930-387A-9D7F-502158595676}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C129638B-4930-387A-9D7F-502158595676}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{3A807A57-8AC1-3338-985F-C9DA58AF63C6}.Debug|x64.ActiveCfg = Debug|x64
		{3A807A57-8AC1-3338-985F-C9DA58AF63C6}.Debug|x64.Build.0 = Debug|x64
		{3A807A57-8AC1-3338-985F-C9DA58AF63C6}.Release|x64.ActiveCfg = Release|x64
		{3A807A57-8AC1-3338-985F-C9DA58AF63C6}.Release|x64.Build.0 = Release|x64
		{3A807A57-8AC1-3338-985F-C9DA58AF63C6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{3A807A57-8AC1-3338-985F-C9DA58AF63C6}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{3A807A57-8AC1-3338-985F-C9DA58AF63C6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{3A807A57-8AC1-3338-985F-C9DA58AF63C6}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4FBEDB73-D215-33DF-B27B-40BF0BC05ADB}.Debug|x64.ActiveCfg = Debug|x64
		{4FBEDB73-D215-33DF-B27B-40BF0BC05ADB}.Debug|x64.Build.0 = Debug|x64
		{4FBEDB73-D215-33DF-B27B-40BF0BC05ADB}.Release|x64.ActiveCfg = Release|x64
		{4FBEDB73-D215-33DF-B27B-40BF0BC05ADB}.Release|x64.Build.0 = Release|x64
		{4FBEDB73-D215-33DF-B27B-40BF0BC05ADB}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4FBEDB73-D215-33DF-B27B-40BF0BC05ADB}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4FBEDB73-D215-33DF-B27B-40BF0BC05ADB}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4FBEDB73-D215-33DF-B27B-40BF0BC05ADB}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8E39805E-45BF-37AB-9D68-D46C8F4B9BC6}.Debug|x64.ActiveCfg = Debug|x64
		{8E39805E-45BF-37AB-9D68-D46C8F4B9BC6}.Debug|x64.Build.0 = Debug|x64
		{8E39805E-45BF-37AB-9D68-D46C8F4B9BC6}.Release|x64.ActiveCfg = Release|x64
		{8E39805E-45BF-37AB-9D68-D46C8F4B9BC6}.Release|x64.Build.0 = Release|x64
		{8E39805E-45BF-37AB-9D68-D46C8F4B9BC6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8E39805E-45BF-37AB-9D68-D46C8F4B9BC6}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8E39805E-45BF-37AB-9D68-D46C8F4B9BC6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8E39805E-45BF-37AB-9D68-D46C8F4B9BC6}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8FA4BFF9-BCD4-3BF3-A7B9-61CF8EF42B0A}.Debug|x64.ActiveCfg = Debug|x64
		{8FA4BFF9-BCD4-3BF3-A7B9-61CF8EF42B0A}.Debug|x64.Build.0 = Debug|x64
		{8FA4BFF9-BCD4-3BF3-A7B9-61CF8EF42B0A}.Release|x64.ActiveCfg = Release|x64
		{8FA4BFF9-BCD4-3BF3-A7B9-61CF8EF42B0A}.Release|x64.Build.0 = Release|x64
		{8FA4BFF9-BCD4-3BF3-A7B9-61CF8EF42B0A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8FA4BFF9-BCD4-3BF3-A7B9-61CF8EF42B0A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8FA4BFF9-BCD4-3BF3-A7B9-61CF8EF42B0A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8FA4BFF9-BCD4-3BF3-A7B9-61CF8EF42B0A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4A490540-92FB-3D63-8FA3-144DE4F42CBD}.Debug|x64.ActiveCfg = Debug|x64
		{4A490540-92FB-3D63-8FA3-144DE4F42CBD}.Debug|x64.Build.0 = Debug|x64
		{4A490540-92FB-3D63-8FA3-144DE4F42CBD}.Release|x64.ActiveCfg = Release|x64
		{4A490540-92FB-3D63-8FA3-144DE4F42CBD}.Release|x64.Build.0 = Release|x64
		{4A490540-92FB-3D63-8FA3-144DE4F42CBD}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4A490540-92FB-3D63-8FA3-144DE4F42CBD}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4A490540-92FB-3D63-8FA3-144DE4F42CBD}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4A490540-92FB-3D63-8FA3-144DE4F42CBD}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F963A301-5191-3348-82F2-C017D32D381E}.Debug|x64.ActiveCfg = Debug|x64
		{F963A301-5191-3348-82F2-C017D32D381E}.Debug|x64.Build.0 = Debug|x64
		{F963A301-5191-3348-82F2-C017D32D381E}.Release|x64.ActiveCfg = Release|x64
		{F963A301-5191-3348-82F2-C017D32D381E}.Release|x64.Build.0 = Release|x64
		{F963A301-5191-3348-82F2-C017D32D381E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F963A301-5191-3348-82F2-C017D32D381E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F963A301-5191-3348-82F2-C017D32D381E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F963A301-5191-3348-82F2-C017D32D381E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{5853A3B2-1393-372B-AE28-29C6E01E403A}.Debug|x64.ActiveCfg = Debug|x64
		{5853A3B2-1393-372B-AE28-29C6E01E403A}.Debug|x64.Build.0 = Debug|x64
		{5853A3B2-1393-372B-AE28-29C6E01E403A}.Release|x64.ActiveCfg = Release|x64
		{5853A3B2-1393-372B-AE28-29C6E01E403A}.Release|x64.Build.0 = Release|x64
		{5853A3B2-1393-372B-AE28-29C6E01E403A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{5853A3B2-1393-372B-AE28-29C6E01E403A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{5853A3B2-1393-372B-AE28-29C6E01E403A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{5853A3B2-1393-372B-AE28-29C6E01E403A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A20B6C67-12DD-3D86-A5FF-4E6EF34CE82D}.Debug|x64.ActiveCfg = Debug|x64
		{A20B6C67-12DD-3D86-A5FF-4E6EF34CE82D}.Debug|x64.Build.0 = Debug|x64
		{A20B6C67-12DD-3D86-A5FF-4E6EF34CE82D}.Release|x64.ActiveCfg = Release|x64
		{A20B6C67-12DD-3D86-A5FF-4E6EF34CE82D}.Release|x64.Build.0 = Release|x64
		{A20B6C67-12DD-3D86-A5FF-4E6EF34CE82D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A20B6C67-12DD-3D86-A5FF-4E6EF34CE82D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A20B6C67-12DD-3D86-A5FF-4E6EF34CE82D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A20B6C67-12DD-3D86-A5FF-4E6EF34CE82D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4D2B3CDD-C9F7-3603-A9B3-B0E02654E57C}.Debug|x64.ActiveCfg = Debug|x64
		{4D2B3CDD-C9F7-3603-A9B3-B0E02654E57C}.Debug|x64.Build.0 = Debug|x64
		{4D2B3CDD-C9F7-3603-A9B3-B0E02654E57C}.Release|x64.ActiveCfg = Release|x64
		{4D2B3CDD-C9F7-3603-A9B3-B0E02654E57C}.Release|x64.Build.0 = Release|x64
		{4D2B3CDD-C9F7-3603-A9B3-B0E02654E57C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4D2B3CDD-C9F7-3603-A9B3-B0E02654E57C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4D2B3CDD-C9F7-3603-A9B3-B0E02654E57C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4D2B3CDD-C9F7-3603-A9B3-B0E02654E57C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.Debug|x64.ActiveCfg = Debug|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.Debug|x64.Build.0 = Debug|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.Release|x64.ActiveCfg = Release|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.Release|x64.Build.0 = Release|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{46CDCC59-30C3-3FC8-A9FC-51CF3414ACC8}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.Debug|x64.ActiveCfg = Debug|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.Debug|x64.Build.0 = Debug|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.Release|x64.ActiveCfg = Release|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.Release|x64.Build.0 = Release|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{60CF3C55-CC5F-3006-A5A5-F06D52DBE861}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{2159201B-1B6E-364F-951F-3D7632A23AFC}.Debug|x64.ActiveCfg = Debug|x64
		{2159201B-1B6E-364F-951F-3D7632A23AFC}.Debug|x64.Build.0 = Debug|x64
		{2159201B-1B6E-364F-951F-3D7632A23AFC}.Release|x64.ActiveCfg = Release|x64
		{2159201B-1B6E-364F-951F-3D7632A23AFC}.Release|x64.Build.0 = Release|x64
		{2159201B-1B6E-364F-951F-3D7632A23AFC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2159201B-1B6E-364F-951F-3D7632A23AFC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{2159201B-1B6E-364F-951F-3D7632A23AFC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2159201B-1B6E-364F-951F-3D7632A23AFC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{55029551-E0D0-3EF7-8998-206701805306}.Debug|x64.ActiveCfg = Debug|x64
		{55029551-E0D0-3EF7-8998-206701805306}.Debug|x64.Build.0 = Debug|x64
		{55029551-E0D0-3EF7-8998-206701805306}.Release|x64.ActiveCfg = Release|x64
		{55029551-E0D0-3EF7-8998-206701805306}.Release|x64.Build.0 = Release|x64
		{55029551-E0D0-3EF7-8998-206701805306}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{55029551-E0D0-3EF7-8998-206701805306}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{55029551-E0D0-3EF7-8998-206701805306}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{55029551-E0D0-3EF7-8998-206701805306}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{CAB65C1F-1D6A-354E-9023-9E5A6222A979}.Debug|x64.ActiveCfg = Debug|x64
		{CAB65C1F-1D6A-354E-9023-9E5A6222A979}.Debug|x64.Build.0 = Debug|x64
		{CAB65C1F-1D6A-354E-9023-9E5A6222A979}.Release|x64.ActiveCfg = Release|x64
		{CAB65C1F-1D6A-354E-9023-9E5A6222A979}.Release|x64.Build.0 = Release|x64
		{CAB65C1F-1D6A-354E-9023-9E5A6222A979}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{CAB65C1F-1D6A-354E-9023-9E5A6222A979}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{CAB65C1F-1D6A-354E-9023-9E5A6222A979}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{CAB65C1F-1D6A-354E-9023-9E5A6222A979}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{95832015-F503-3E99-9279-AC3F77E5444B}.Debug|x64.ActiveCfg = Debug|x64
		{95832015-F503-3E99-9279-AC3F77E5444B}.Debug|x64.Build.0 = Debug|x64
		{95832015-F503-3E99-9279-AC3F77E5444B}.Release|x64.ActiveCfg = Release|x64
		{95832015-F503-3E99-9279-AC3F77E5444B}.Release|x64.Build.0 = Release|x64
		{95832015-F503-3E99-9279-AC3F77E5444B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{95832015-F503-3E99-9279-AC3F77E5444B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{95832015-F503-3E99-9279-AC3F77E5444B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{95832015-F503-3E99-9279-AC3F77E5444B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{492769B1-BC82-355C-B717-024749764B8A}.Debug|x64.ActiveCfg = Debug|x64
		{492769B1-BC82-355C-B717-024749764B8A}.Debug|x64.Build.0 = Debug|x64
		{492769B1-BC82-355C-B717-024749764B8A}.Release|x64.ActiveCfg = Release|x64
		{492769B1-BC82-355C-B717-024749764B8A}.Release|x64.Build.0 = Release|x64
		{492769B1-BC82-355C-B717-024749764B8A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{492769B1-BC82-355C-B717-024749764B8A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{492769B1-BC82-355C-B717-024749764B8A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{492769B1-BC82-355C-B717-024749764B8A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{207D94D9-DA8F-355A-BF59-A4C8E3CA14EF}.Debug|x64.ActiveCfg = Debug|x64
		{207D94D9-DA8F-355A-BF59-A4C8E3CA14EF}.Debug|x64.Build.0 = Debug|x64
		{207D94D9-DA8F-355A-BF59-A4C8E3CA14EF}.Release|x64.ActiveCfg = Release|x64
		{207D94D9-DA8F-355A-BF59-A4C8E3CA14EF}.Release|x64.Build.0 = Release|x64
		{207D94D9-DA8F-355A-BF59-A4C8E3CA14EF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{207D94D9-DA8F-355A-BF59-A4C8E3CA14EF}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{207D94D9-DA8F-355A-BF59-A4C8E3CA14EF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{207D94D9-DA8F-355A-BF59-A4C8E3CA14EF}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{BAB254DC-65D8-3B0D-B5F4-3CCEDCEA355E}.Debug|x64.ActiveCfg = Debug|x64
		{BAB254DC-65D8-3B0D-B5F4-3CCEDCEA355E}.Debug|x64.Build.0 = Debug|x64
		{BAB254DC-65D8-3B0D-B5F4-3CCEDCEA355E}.Release|x64.ActiveCfg = Release|x64
		{BAB254DC-65D8-3B0D-B5F4-3CCEDCEA355E}.Release|x64.Build.0 = Release|x64
		{BAB254DC-65D8-3B0D-B5F4-3CCEDCEA355E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BAB254DC-65D8-3B0D-B5F4-3CCEDCEA355E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{BAB254DC-65D8-3B0D-B5F4-3CCEDCEA355E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{BAB254DC-65D8-3B0D-B5F4-3CCEDCEA355E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{839C181D-28DA-382E-A6D1-9C5F18411D1D}.Debug|x64.ActiveCfg = Debug|x64
		{839C181D-28DA-382E-A6D1-9C5F18411D1D}.Debug|x64.Build.0 = Debug|x64
		{839C181D-28DA-382E-A6D1-9C5F18411D1D}.Release|x64.ActiveCfg = Release|x64
		{839C181D-28DA-382E-A6D1-9C5F18411D1D}.Release|x64.Build.0 = Release|x64
		{839C181D-28DA-382E-A6D1-9C5F18411D1D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{839C181D-28DA-382E-A6D1-9C5F18411D1D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{839C181D-28DA-382E-A6D1-9C5F18411D1D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{839C181D-28DA-382E-A6D1-9C5F18411D1D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{FCFD7D21-2146-3CBF-83F0-10812F78D9B4}.Debug|x64.ActiveCfg = Debug|x64
		{FCFD7D21-2146-3CBF-83F0-10812F78D9B4}.Debug|x64.Build.0 = Debug|x64
		{FCFD7D21-2146-3CBF-83F0-10812F78D9B4}.Release|x64.ActiveCfg = Release|x64
		{FCFD7D21-2146-3CBF-83F0-10812F78D9B4}.Release|x64.Build.0 = Release|x64
		{FCFD7D21-2146-3CBF-83F0-10812F78D9B4}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FCFD7D21-2146-3CBF-83F0-10812F78D9B4}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{FCFD7D21-2146-3CBF-83F0-10812F78D9B4}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FCFD7D21-2146-3CBF-83F0-10812F78D9B4}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{9C6DA0B4-C233-32CF-8243-2A9CE50FFC99}.Debug|x64.ActiveCfg = Debug|x64
		{9C6DA0B4-C233-32CF-8243-2A9CE50FFC99}.Debug|x64.Build.0 = Debug|x64
		{9C6DA0B4-C233-32CF-8243-2A9CE50FFC99}.Release|x64.ActiveCfg = Release|x64
		{9C6DA0B4-C233-32CF-8243-2A9CE50FFC99}.Release|x64.Build.0 = Release|x64
		{9C6DA0B4-C233-32CF-8243-2A9CE50FFC99}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9C6DA0B4-C233-32CF-8243-2A9CE50FFC99}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9C6DA0B4-C233-32CF-8243-2A9CE50FFC99}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9C6DA0B4-C233-32CF-8243-2A9CE50FFC99}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A998C219-A034-3AF9-BED5-DF7A7109E126}.Debug|x64.ActiveCfg = Debug|x64
		{A998C219-A034-3AF9-BED5-DF7A7109E126}.Debug|x64.Build.0 = Debug|x64
		{A998C219-A034-3AF9-BED5-DF7A7109E126}.Release|x64.ActiveCfg = Release|x64
		{A998C219-A034-3AF9-BED5-DF7A7109E126}.Release|x64.Build.0 = Release|x64
		{A998C219-A034-3AF9-BED5-DF7A7109E126}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A998C219-A034-3AF9-BED5-DF7A7109E126}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A998C219-A034-3AF9-BED5-DF7A7109E126}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A998C219-A034-3AF9-BED5-DF7A7109E126}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{7B028B37-669C-34D3-AC69-ADD0E748555C}.Debug|x64.ActiveCfg = Debug|x64
		{7B028B37-669C-34D3-AC69-ADD0E748555C}.Debug|x64.Build.0 = Debug|x64
		{7B028B37-669C-34D3-AC69-ADD0E748555C}.Release|x64.ActiveCfg = Release|x64
		{7B028B37-669C-34D3-AC69-ADD0E748555C}.Release|x64.Build.0 = Release|x64
		{7B028B37-669C-34D3-AC69-ADD0E748555C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7B028B37-669C-34D3-AC69-ADD0E748555C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7B028B37-669C-34D3-AC69-ADD0E748555C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7B028B37-669C-34D3-AC69-ADD0E748555C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4C1F4BB3-E99B-39B3-A75E-D9FE17F0C595}.Debug|x64.ActiveCfg = Debug|x64
		{4C1F4BB3-E99B-39B3-A75E-D9FE17F0C595}.Debug|x64.Build.0 = Debug|x64
		{4C1F4BB3-E99B-39B3-A75E-D9FE17F0C595}.Release|x64.ActiveCfg = Release|x64
		{4C1F4BB3-E99B-39B3-A75E-D9FE17F0C595}.Release|x64.Build.0 = Release|x64
		{4C1F4BB3-E99B-39B3-A75E-D9FE17F0C595}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4C1F4BB3-E99B-39B3-A75E-D9FE17F0C595}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4C1F4BB3-E99B-39B3-A75E-D9FE17F0C595}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4C1F4BB3-E99B-39B3-A75E-D9FE17F0C595}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F2A19983-B04A-344B-88F1-BD19733FD5AF}.Debug|x64.ActiveCfg = Debug|x64
		{F2A19983-B04A-344B-88F1-BD19733FD5AF}.Debug|x64.Build.0 = Debug|x64
		{F2A19983-B04A-344B-88F1-BD19733FD5AF}.Release|x64.ActiveCfg = Release|x64
		{F2A19983-B04A-344B-88F1-BD19733FD5AF}.Release|x64.Build.0 = Release|x64
		{F2A19983-B04A-344B-88F1-BD19733FD5AF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F2A19983-B04A-344B-88F1-BD19733FD5AF}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F2A19983-B04A-344B-88F1-BD19733FD5AF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F2A19983-B04A-344B-88F1-BD19733FD5AF}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{FB77B135-8F5E-36AF-9136-D7F81F402B26}.Debug|x64.ActiveCfg = Debug|x64
		{FB77B135-8F5E-36AF-9136-D7F81F402B26}.Debug|x64.Build.0 = Debug|x64
		{FB77B135-8F5E-36AF-9136-D7F81F402B26}.Release|x64.ActiveCfg = Release|x64
		{FB77B135-8F5E-36AF-9136-D7F81F402B26}.Release|x64.Build.0 = Release|x64
		{FB77B135-8F5E-36AF-9136-D7F81F402B26}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FB77B135-8F5E-36AF-9136-D7F81F402B26}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{FB77B135-8F5E-36AF-9136-D7F81F402B26}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FB77B135-8F5E-36AF-9136-D7F81F402B26}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}.Debug|x64.ActiveCfg = Debug|x64
		{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}.Debug|x64.Build.0 = Debug|x64
		{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}.Release|x64.ActiveCfg = Release|x64
		{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}.Release|x64.Build.0 = Release|x64
		{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F4B48AC5-5EB2-3CEF-B3E8-7DA9B382137F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{87EC0E93-8839-3F34-B1A2-D89CE2276646}.Debug|x64.ActiveCfg = Debug|x64
		{87EC0E93-8839-3F34-B1A2-D89CE2276646}.Debug|x64.Build.0 = Debug|x64
		{87EC0E93-8839-3F34-B1A2-D89CE2276646}.Release|x64.ActiveCfg = Release|x64
		{87EC0E93-8839-3F34-B1A2-D89CE2276646}.Release|x64.Build.0 = Release|x64
		{87EC0E93-8839-3F34-B1A2-D89CE2276646}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{87EC0E93-8839-3F34-B1A2-D89CE2276646}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{87EC0E93-8839-3F34-B1A2-D89CE2276646}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{87EC0E93-8839-3F34-B1A2-D89CE2276646}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{610E614D-95B6-3CF5-983D-15B9DB6560E6}.Debug|x64.ActiveCfg = Debug|x64
		{610E614D-95B6-3CF5-983D-15B9DB6560E6}.Debug|x64.Build.0 = Debug|x64
		{610E614D-95B6-3CF5-983D-15B9DB6560E6}.Release|x64.ActiveCfg = Release|x64
		{610E614D-95B6-3CF5-983D-15B9DB6560E6}.Release|x64.Build.0 = Release|x64
		{610E614D-95B6-3CF5-983D-15B9DB6560E6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{610E614D-95B6-3CF5-983D-15B9DB6560E6}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{610E614D-95B6-3CF5-983D-15B9DB6560E6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{610E614D-95B6-3CF5-983D-15B9DB6560E6}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{169CEAE6-AC64-395A-A1A5-1773B9DE5FA3}.Debug|x64.ActiveCfg = Debug|x64
		{169CEAE6-AC64-395A-A1A5-1773B9DE5FA3}.Debug|x64.Build.0 = Debug|x64
		{169CEAE6-AC64-395A-A1A5-1773B9DE5FA3}.Release|x64.ActiveCfg = Release|x64
		{169CEAE6-AC64-395A-A1A5-1773B9DE5FA3}.Release|x64.Build.0 = Release|x64
		{169CEAE6-AC64-395A-A1A5-1773B9DE5FA3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{169CEAE6-AC64-395A-A1A5-1773B9DE5FA3}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{169CEAE6-AC64-395A-A1A5-1773B9DE5FA3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{169CEAE6-AC64-395A-A1A5-1773B9DE5FA3}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{9BCA4545-8534-38B7-A366-D76C2A72E8FE}.Debug|x64.ActiveCfg = Debug|x64
		{9BCA4545-8534-38B7-A366-D76C2A72E8FE}.Debug|x64.Build.0 = Debug|x64
		{9BCA4545-8534-38B7-A366-D76C2A72E8FE}.Release|x64.ActiveCfg = Release|x64
		{9BCA4545-8534-38B7-A366-D76C2A72E8FE}.Release|x64.Build.0 = Release|x64
		{9BCA4545-8534-38B7-A366-D76C2A72E8FE}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9BCA4545-8534-38B7-A366-D76C2A72E8FE}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9BCA4545-8534-38B7-A366-D76C2A72E8FE}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9BCA4545-8534-38B7-A366-D76C2A72E8FE}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4CE14F60-9B4A-3059-A868-C38F6759921E}.Debug|x64.ActiveCfg = Debug|x64
		{4CE14F60-9B4A-3059-A868-C38F6759921E}.Debug|x64.Build.0 = Debug|x64
		{4CE14F60-9B4A-3059-A868-C38F6759921E}.Release|x64.ActiveCfg = Release|x64
		{4CE14F60-9B4A-3059-A868-C38F6759921E}.Release|x64.Build.0 = Release|x64
		{4CE14F60-9B4A-3059-A868-C38F6759921E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4CE14F60-9B4A-3059-A868-C38F6759921E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4CE14F60-9B4A-3059-A868-C38F6759921E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4CE14F60-9B4A-3059-A868-C38F6759921E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{D3979881-D853-3376-83BA-4C2E22043EEB}.Debug|x64.ActiveCfg = Debug|x64
		{D3979881-D853-3376-83BA-4C2E22043EEB}.Debug|x64.Build.0 = Debug|x64
		{D3979881-D853-3376-83BA-4C2E22043EEB}.Release|x64.ActiveCfg = Release|x64
		{D3979881-D853-3376-83BA-4C2E22043EEB}.Release|x64.Build.0 = Release|x64
		{D3979881-D853-3376-83BA-4C2E22043EEB}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D3979881-D853-3376-83BA-4C2E22043EEB}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{D3979881-D853-3376-83BA-4C2E22043EEB}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D3979881-D853-3376-83BA-4C2E22043EEB}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{AD25EBA1-BE06-384A-8390-574D35B2CEA3}.Debug|x64.ActiveCfg = Debug|x64
		{AD25EBA1-BE06-384A-8390-574D35B2CEA3}.Debug|x64.Build.0 = Debug|x64
		{AD25EBA1-BE06-384A-8390-574D35B2CEA3}.Release|x64.ActiveCfg = Release|x64
		{AD25EBA1-BE06-384A-8390-574D35B2CEA3}.Release|x64.Build.0 = Release|x64
		{AD25EBA1-BE06-384A-8390-574D35B2CEA3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{AD25EBA1-BE06-384A-8390-574D35B2CEA3}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{AD25EBA1-BE06-384A-8390-574D35B2CEA3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{AD25EBA1-BE06-384A-8390-574D35B2CEA3}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4F74D0DA-53E5-3C2C-B355-2D0BFDB16385}.Debug|x64.ActiveCfg = Debug|x64
		{4F74D0DA-53E5-3C2C-B355-2D0BFDB16385}.Debug|x64.Build.0 = Debug|x64
		{4F74D0DA-53E5-3C2C-B355-2D0BFDB16385}.Release|x64.ActiveCfg = Release|x64
		{4F74D0DA-53E5-3C2C-B355-2D0BFDB16385}.Release|x64.Build.0 = Release|x64
		{4F74D0DA-53E5-3C2C-B355-2D0BFDB16385}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4F74D0DA-53E5-3C2C-B355-2D0BFDB16385}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4F74D0DA-53E5-3C2C-B355-2D0BFDB16385}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4F74D0DA-53E5-3C2C-B355-2D0BFDB16385}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{41D409D0-14D4-38E1-A74C-4DA2F02CF51B}.Debug|x64.ActiveCfg = Debug|x64
		{41D409D0-14D4-38E1-A74C-4DA2F02CF51B}.Debug|x64.Build.0 = Debug|x64
		{41D409D0-14D4-38E1-A74C-4DA2F02CF51B}.Release|x64.ActiveCfg = Release|x64
		{41D409D0-14D4-38E1-A74C-4DA2F02CF51B}.Release|x64.Build.0 = Release|x64
		{41D409D0-14D4-38E1-A74C-4DA2F02CF51B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{41D409D0-14D4-38E1-A74C-4DA2F02CF51B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{41D409D0-14D4-38E1-A74C-4DA2F02CF51B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{41D409D0-14D4-38E1-A74C-4DA2F02CF51B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{D0A34FE7-72E3-38B7-8DC7-519F82E46F38}.Debug|x64.ActiveCfg = Debug|x64
		{D0A34FE7-72E3-38B7-8DC7-519F82E46F38}.Debug|x64.Build.0 = Debug|x64
		{D0A34FE7-72E3-38B7-8DC7-519F82E46F38}.Release|x64.ActiveCfg = Release|x64
		{D0A34FE7-72E3-38B7-8DC7-519F82E46F38}.Release|x64.Build.0 = Release|x64
		{D0A34FE7-72E3-38B7-8DC7-519F82E46F38}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D0A34FE7-72E3-38B7-8DC7-519F82E46F38}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{D0A34FE7-72E3-38B7-8DC7-519F82E46F38}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D0A34FE7-72E3-38B7-8DC7-519F82E46F38}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{5902CDDD-E76B-3D9A-9181-B0EC767F81FB}.Debug|x64.ActiveCfg = Debug|x64
		{5902CDDD-E76B-3D9A-9181-B0EC767F81FB}.Debug|x64.Build.0 = Debug|x64
		{5902CDDD-E76B-3D9A-9181-B0EC767F81FB}.Release|x64.ActiveCfg = Release|x64
		{5902CDDD-E76B-3D9A-9181-B0EC767F81FB}.Release|x64.Build.0 = Release|x64
		{5902CDDD-E76B-3D9A-9181-B0EC767F81FB}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{5902CDDD-E76B-3D9A-9181-B0EC767F81FB}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{5902CDDD-E76B-3D9A-9181-B0EC767F81FB}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{5902CDDD-E76B-3D9A-9181-B0EC767F81FB}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F66C6BCB-E4CB-375B-8CBD-6A4D489B0FD7}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
