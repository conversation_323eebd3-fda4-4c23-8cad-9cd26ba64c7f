{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "fuxicore/Release/fuxicore.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "add_unique_libraries", "add_definitions", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/library.cmake", "fuxicore/CMakeLists.txt", "builder/cmake/add_openssl.cmake", "builder/cmake/common.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_xerces-c.cmake", "builder/cmake/add_opencv.cmake", "builder/cmake/add_rttr.cmake", "builder/cmake/add_boost.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 76, "parent": 2}, {"command": 3, "file": 0, "line": 78, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 4}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 3, "parent": 6}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 2, "parent": 8}, {"command": 4, "file": 2, "line": 5, "parent": 9}, {"command": 5, "file": 0, "line": 68, "parent": 2}, {"command": 5, "file": 0, "line": 64, "parent": 2}, {"command": 5, "file": 0, "line": 67, "parent": 2}, {"command": 3, "file": 0, "line": 84, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 14}, {"command": 5, "file": 0, "line": 65, "parent": 2}, {"command": 3, "file": 0, "line": 84, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 17}, {"command": 3, "file": 0, "line": 84, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 19}, {"command": 5, "file": 0, "line": 66, "parent": 2}, {"command": 6, "file": 3, "line": 54, "parent": 7}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 4, "parent": 23}, {"command": 6, "file": 4, "line": 16, "parent": 24}, {"command": 6, "file": 2, "line": 23, "parent": 9}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 5, "parent": 27}, {"command": 6, "file": 5, "line": 11, "parent": 28}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 6, "parent": 30}, {"command": 6, "file": 6, "line": 23, "parent": 31}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 7, "parent": 33}, {"command": 6, "file": 7, "line": 29, "parent": 34}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 8, "parent": 36}, {"command": 6, "file": 8, "line": 40, "parent": 37}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 3, "file": 0, "line": 84, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 45}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 10, "define": "CPPHTTPLIB_OPENSSL_SUPPORT"}, {"backtrace": 11, "define": "QT_CORE_LIB"}, {"backtrace": 12, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 13, "define": "QT_GUI_LIB"}, {"backtrace": 15, "define": "QT_NETWORK_LIB"}, {"backtrace": 16, "define": "QT_NO_DEBUG"}, {"backtrace": 18, "define": "QT_SERIALPORT_LIB"}, {"backtrace": 20, "define": "QT_SQL_LIB"}, {"backtrace": 21, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 0, "path": "D:/newFuxios/build/fuxicore/fuxicore_autogen/include_Release"}, {"backtrace": 22, "path": "D:/newFuxios/fuxicore/include"}, {"backtrace": 25, "path": "C:/opt/glog/include"}, {"backtrace": 26, "path": "C:/opt/openssl/include"}, {"backtrace": 29, "path": "C:/opt/xerces-c/include"}, {"backtrace": 32, "path": "C:/opt/opencv/build/include"}, {"backtrace": 35, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 38, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 39, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 39, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 39, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 40, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 40, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 41, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 42, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}, {"backtrace": 43, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtNetwork"}, {"backtrace": 44, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSerialPort"}], "language": "CXX", "languageStandard": {"backtraces": [5, 46], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "fuxicore::@5cdc2d2ab21b3b9e72cd", "name": "fuxicore", "nameOnDisk": "fuxicore.lib", "paths": {"build": "fuxicore", "source": "fuxicore"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/fuxicore/fuxicore_autogen/mocs_compilation_Release.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/base/FileUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/base/MathUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/base/StringUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/base/TimeUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/crypto/CryptoUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/crypto/qaesencryption.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/data/ConfigManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/data/JsonUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/hardware/SerialManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/management/LicenseManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/management/LogUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/network/NetworkUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/network/SocketManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/system/SystemMonitor.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicore/src/system/ThreadUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "fuxicore/include/base/FileUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/base/MathUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/base/StringUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/base/TimeUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/crypto/CryptoUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/crypto/aesni/aesni-enc-cbc.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/crypto/aesni/aesni-enc-ecb.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/crypto/aesni/aesni-key-exp.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/crypto/aesni/aesni-key-init.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/crypto/qaesencryption.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/data/ConfigManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/data/JsonUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/fuxicore.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/fuxiutils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/hardware/SerialManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/management/LicenseManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/management/LogUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/network/NetworkUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/network/SocketManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/system/SystemMonitor.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicore/include/system/ThreadUtils.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}