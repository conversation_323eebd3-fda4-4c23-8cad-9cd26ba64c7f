{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "hardwaredriver/fairinoDriver/Release/hardwaredriverfairinoDriver.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "add_unique_libraries", "add_definitions", "find_package", "include_directories"], "files": ["builder/cmake/library.cmake", "hardwaredriver/fairinoDriver/CMakeLists.txt", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkConfig.cmake", "builder/cmake/add_robwork.cmake", "builder/cmake/common.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_libfairino.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 2, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 88, "parent": 2}, {"command": 3, "file": 0, "line": 90, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 4}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 4, "parent": 6}, {"command": 1, "file": 4, "line": 81, "parent": 7}, {"file": 3, "parent": 8}, {"command": 5, "file": 3, "line": 18, "parent": 9}, {"file": 2, "parent": 10}, {"command": 4, "file": 2, "line": 310, "parent": 11}, {"command": 6, "file": 4, "line": 54, "parent": 7}, {"command": 1, "file": 4, "line": 81, "parent": 7}, {"file": 5, "parent": 14}, {"command": 6, "file": 5, "line": 16, "parent": 15}, {"command": 6, "file": 3, "line": 21, "parent": 9}, {"command": 1, "file": 4, "line": 81, "parent": 7}, {"file": 6, "parent": 18}, {"command": 6, "file": 6, "line": 11, "parent": 19}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc -EHa -bigobj /MP -openmp /O2 /Ob2 /DNDEBUG -std:c++20 -MD"}], "defines": [{"backtrace": 12, "define": "BIND_FORTRAN_LOWERCASE_UNDERSCORE"}, {"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 12, "define": "MSVC_AMD64"}, {"backtrace": 12, "define": "NOMINMAX"}, {"backtrace": 5, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}, {"backtrace": 12, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 12, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 12, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 12, "define": "_SCL_SECURE_NO_WARNINGS"}, {"backtrace": 12, "define": "_WIN32_WINNT=0x0501"}], "includes": [{"backtrace": 13, "path": "D:/newFuxios/hardwaredriver/fairinoDriver/include"}, {"backtrace": 16, "path": "C:/opt/glog/include"}, {"backtrace": 17, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3"}, {"backtrace": 17, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12"}, {"backtrace": 17, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi"}, {"backtrace": 17, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp"}, {"backtrace": 17, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src"}, {"backtrace": 17, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src"}, {"backtrace": 17, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib"}, {"backtrace": 17, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include"}, {"backtrace": 17, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include"}, {"backtrace": 17, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext"}, {"backtrace": 20, "path": "C:/opt/libfairino/include"}, {"backtrace": 5, "path": "D:/newFuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 17, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "hardwaredriverfairinoDriver::@8a675cd9715b77cebac5", "name": "hardwaredriverfairinoDriver", "nameOnDisk": "hardwaredriverfairinoDriver.lib", "paths": {"build": "hardwaredriver/fairinoDriver", "source": "hardwaredriver/fairinoDriver"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "hardwaredriver/fairinoDriver/src/fairinoDriver.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "hardwaredriver/fairinoDriver/include/fairinoDriver.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}