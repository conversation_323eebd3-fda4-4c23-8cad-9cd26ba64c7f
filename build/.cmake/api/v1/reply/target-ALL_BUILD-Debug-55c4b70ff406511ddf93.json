{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["RoboticLaserMarking/LicenseGenerator/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "RoboticLaserMarkingLicenseGenerator::@59f7d9ae5c13d347c5f4"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "ALL_BUILD::@59f7d9ae5c13d347c5f4", "isGeneratorProvided": true, "name": "ALL_BUILD", "paths": {"build": "RoboticLaserMarking/LicenseGenerator", "source": "RoboticLaserMarking/LicenseGenerator"}, "sources": [], "type": "UTILITY"}