{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "RoboticLaserMarking/RFIDDriver/RelWithDebInfo/RoboticLaserMarkingRFIDDriver.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "include_directories"], "files": ["builder/cmake/library.cmake", "RoboticLaserMarking/RFIDDriver/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/add_eigen.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_libmodbus.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 2, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 88, "parent": 2}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 4}, {"command": 2, "file": 2, "line": 54, "parent": 5}, {"command": 1, "file": 2, "line": 81, "parent": 5}, {"file": 3, "parent": 7}, {"command": 2, "file": 3, "line": 30, "parent": 8}, {"command": 1, "file": 2, "line": 81, "parent": 5}, {"file": 4, "parent": 10}, {"command": 2, "file": 4, "line": 16, "parent": 11}, {"command": 1, "file": 2, "line": 81, "parent": 5}, {"file": 5, "parent": 13}, {"command": 2, "file": 5, "line": 8, "parent": 14}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -std:c++20 -MD"}], "includes": [{"backtrace": 6, "path": "D:/newFuxios/RoboticLaserMarking/RFIDDriver/include"}, {"backtrace": 9, "path": "C:/opt/PCL/3rdParty/Eigen/eigen3"}, {"backtrace": 12, "path": "C:/opt/glog/include"}, {"backtrace": 15, "path": "C:/opt/libmodbus/include"}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "20"}, "sourceIndexes": [0]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "RoboticLaserMarkingRFIDDriver::@d1520424919af3a40272", "name": "RoboticLaserMarkingRFIDDriver", "nameOnDisk": "RoboticLaserMarkingRFIDDriver.lib", "paths": {"build": "RoboticLaserMarking/RFIDDriver", "source": "RoboticLaserMarking/RFIDDriver"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/RFIDDriver/src/RFIDModbusDriver.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "RoboticLaserMarking/RFIDDriver/include/RFIDModbusDriver.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}