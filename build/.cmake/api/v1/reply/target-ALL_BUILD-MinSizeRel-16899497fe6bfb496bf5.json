{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["tool/communication/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "toolcommunication::@116eb0f160f4d76de168"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "ALL_BUILD::@116eb0f160f4d76de168", "isGeneratorProvided": true, "name": "ALL_BUILD", "paths": {"build": "tool/communication", "source": "tool/communication"}, "sources": [], "type": "UTILITY"}