{"artifacts": [{"path": "D:/newFuxios/install/x64-install/dev/bin/MinSizeRel/toolhandeyecaluihandeyecalui.exe"}, {"path": "D:/newFuxios/install/x64-install/dev/bin/MinSizeRel/toolhandeyecaluihandeyecalui.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "set_property", "find_package", "boost_find_component", "add_definitions", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/executable.cmake", "tool/handeyecalui/handeyecalui/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/library.cmake", "hardwaredriver/HikVisionCamera/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "fuxicommon/CMakeLists.txt", "hardwaredriver/AuboArcsDriver/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkConfig.cmake", "builder/cmake/add_robwork.cmake", "builder/cmake/add_eigen.cmake", "builder/cmake/add_opencv.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_aubo.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 26, "parent": 2}, {"command": 3, "file": 0, "line": 30, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 6}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 8}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 10}, {"command": 2, "file": 0, "line": 46, "parent": 2}, {"file": 4}, {"command": 1, "file": 4, "line": 3, "parent": 13}, {"file": 3, "parent": 14}, {"command": 3, "file": 3, "line": 78, "parent": 15}, {"command": 2, "file": 3, "line": 53, "parent": 16}, {"command": 1, "file": 3, "line": 1, "parent": 15}, {"file": 2, "parent": 18}, {"command": 1, "file": 2, "line": 81, "parent": 19}, {"file": 8, "parent": 20}, {"command": 5, "file": 8, "line": 33, "parent": 21}, {"file": 7, "parent": 22}, {"command": 5, "file": 7, "line": 610, "parent": 23}, {"file": 6, "parent": 24}, {"command": 6, "file": 6, "line": 262, "parent": 25}, {"command": 5, "file": 6, "line": 141, "parent": 26}, {"file": 5, "parent": 27}, {"command": 4, "file": 5, "line": 103, "parent": 28}, {"command": 6, "file": 6, "line": 262, "parent": 25}, {"command": 5, "file": 6, "line": 141, "parent": 30}, {"file": 9, "parent": 31}, {"command": 4, "file": 9, "line": 103, "parent": 32}, {"command": 3, "file": 3, "line": 84, "parent": 15}, {"command": 2, "file": 3, "line": 53, "parent": 34}, {"command": 3, "file": 3, "line": 84, "parent": 15}, {"command": 2, "file": 3, "line": 53, "parent": 36}, {"command": 3, "file": 3, "line": 84, "parent": 15}, {"command": 2, "file": 3, "line": 53, "parent": 38}, {"command": 3, "file": 3, "line": 84, "parent": 15}, {"command": 2, "file": 3, "line": 53, "parent": 40}, {"file": 10}, {"command": 1, "file": 10, "line": 3, "parent": 42}, {"file": 3, "parent": 43}, {"command": 3, "file": 3, "line": 78, "parent": 44}, {"command": 2, "file": 3, "line": 53, "parent": 45}, {"command": 1, "file": 3, "line": 1, "parent": 44}, {"file": 2, "parent": 47}, {"command": 1, "file": 2, "line": 81, "parent": 48}, {"file": 8, "parent": 49}, {"command": 5, "file": 8, "line": 33, "parent": 50}, {"file": 7, "parent": 51}, {"command": 5, "file": 7, "line": 610, "parent": 52}, {"file": 6, "parent": 53}, {"command": 6, "file": 6, "line": 262, "parent": 54}, {"command": 5, "file": 6, "line": 141, "parent": 55}, {"file": 5, "parent": 56}, {"command": 4, "file": 5, "line": 103, "parent": 57}, {"command": 6, "file": 6, "line": 262, "parent": 54}, {"command": 5, "file": 6, "line": 141, "parent": 59}, {"file": 9, "parent": 60}, {"command": 4, "file": 9, "line": 103, "parent": 61}, {"command": 3, "file": 3, "line": 84, "parent": 44}, {"command": 2, "file": 3, "line": 53, "parent": 63}, {"command": 3, "file": 3, "line": 84, "parent": 44}, {"command": 2, "file": 3, "line": 53, "parent": 65}, {"command": 3, "file": 3, "line": 84, "parent": 44}, {"command": 2, "file": 3, "line": 53, "parent": 67}, {"command": 3, "file": 3, "line": 84, "parent": 44}, {"command": 2, "file": 3, "line": 53, "parent": 69}, {"file": 11}, {"command": 1, "file": 11, "line": 3, "parent": 71}, {"file": 3, "parent": 72}, {"command": 3, "file": 3, "line": 78, "parent": 73}, {"command": 2, "file": 3, "line": 53, "parent": 74}, {"command": 1, "file": 3, "line": 1, "parent": 73}, {"file": 2, "parent": 76}, {"command": 1, "file": 2, "line": 81, "parent": 77}, {"file": 8, "parent": 78}, {"command": 5, "file": 8, "line": 33, "parent": 79}, {"file": 7, "parent": 80}, {"command": 5, "file": 7, "line": 610, "parent": 81}, {"file": 6, "parent": 82}, {"command": 6, "file": 6, "line": 262, "parent": 83}, {"command": 5, "file": 6, "line": 141, "parent": 84}, {"file": 5, "parent": 85}, {"command": 4, "file": 5, "line": 103, "parent": 86}, {"command": 6, "file": 6, "line": 262, "parent": 83}, {"command": 5, "file": 6, "line": 141, "parent": 88}, {"file": 9, "parent": 89}, {"command": 4, "file": 9, "line": 103, "parent": 90}, {"command": 3, "file": 3, "line": 84, "parent": 73}, {"command": 2, "file": 3, "line": 53, "parent": 92}, {"command": 3, "file": 3, "line": 84, "parent": 73}, {"command": 2, "file": 3, "line": 53, "parent": 94}, {"command": 3, "file": 3, "line": 84, "parent": 73}, {"command": 2, "file": 3, "line": 53, "parent": 96}, {"command": 3, "file": 3, "line": 84, "parent": 73}, {"command": 2, "file": 3, "line": 53, "parent": 98}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 100}, {"command": 1, "file": 2, "line": 81, "parent": 101}, {"file": 8, "parent": 102}, {"command": 5, "file": 8, "line": 33, "parent": 103}, {"file": 7, "parent": 104}, {"command": 5, "file": 7, "line": 610, "parent": 105}, {"file": 6, "parent": 106}, {"command": 6, "file": 6, "line": 262, "parent": 107}, {"command": 5, "file": 6, "line": 141, "parent": 108}, {"file": 5, "parent": 109}, {"command": 1, "file": 5, "line": 53, "parent": 110}, {"file": 12, "parent": 111}, {"command": 4, "file": 12, "line": 101, "parent": 112}, {"command": 4, "file": 5, "line": 103, "parent": 110}, {"command": 6, "file": 6, "line": 262, "parent": 107}, {"command": 5, "file": 6, "line": 141, "parent": 115}, {"file": 9, "parent": 116}, {"command": 4, "file": 9, "line": 103, "parent": 117}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 119}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 121}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 123}, {"command": 1, "file": 2, "line": 81, "parent": 101}, {"file": 14, "parent": 125}, {"command": 5, "file": 14, "line": 18, "parent": 126}, {"file": 13, "parent": 127}, {"command": 7, "file": 13, "line": 310, "parent": 128}, {"command": 8, "file": 0, "line": 17, "parent": 2}, {"command": 8, "file": 0, "line": 13, "parent": 2}, {"command": 8, "file": 0, "line": 16, "parent": 2}, {"command": 8, "file": 0, "line": 14, "parent": 2}, {"command": 8, "file": 0, "line": 15, "parent": 2}, {"command": 9, "file": 2, "line": 54, "parent": 101}, {"command": 1, "file": 2, "line": 81, "parent": 101}, {"file": 15, "parent": 136}, {"command": 9, "file": 15, "line": 30, "parent": 137}, {"command": 1, "file": 2, "line": 81, "parent": 101}, {"file": 16, "parent": 139}, {"command": 9, "file": 16, "line": 23, "parent": 140}, {"command": 1, "file": 2, "line": 81, "parent": 101}, {"file": 17, "parent": 142}, {"command": 9, "file": 17, "line": 16, "parent": 143}, {"command": 1, "file": 2, "line": 81, "parent": 101}, {"file": 18, "parent": 145}, {"command": 9, "file": 18, "line": 8, "parent": 146}, {"command": 9, "file": 14, "line": 21, "parent": 126}, {"command": 9, "file": 8, "line": 40, "parent": 103}, {"command": 9, "file": 0, "line": 34, "parent": 2}, {"command": 9, "file": 0, "line": 34, "parent": 2}, {"command": 9, "file": 0, "line": 34, "parent": 2}, {"command": 9, "file": 0, "line": 34, "parent": 2}, {"command": 9, "file": 0, "line": 34, "parent": 2}, {"command": 9, "file": 0, "line": 34, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc -EHa -bigobj /MP -openmp /O1 /Ob1 /DNDEBUG -std:c++20 -MD"}], "defines": [{"backtrace": 129, "define": "BIND_FORTRAN_LOWERCASE_UNDERSCORE"}, {"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 129, "define": "MSVC_AMD64"}, {"backtrace": 129, "define": "NOMINMAX"}, {"backtrace": 9, "define": "QT_CHARTS_LIB"}, {"backtrace": 130, "define": "QT_CORE_LIB"}, {"backtrace": 131, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 132, "define": "QT_GUI_LIB"}, {"backtrace": 133, "define": "QT_NO_DEBUG"}, {"backtrace": 11, "define": "QT_SERIALPORT_LIB"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 134, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}, {"backtrace": 129, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 129, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 129, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 129, "define": "_SCL_SECURE_NO_WARNINGS"}, {"backtrace": 129, "define": "_WIN32_WINNT=0x0501"}], "includes": [{"backtrace": 0, "path": "D:/newFuxios/build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen/include_MinSizeRel"}, {"backtrace": 135, "path": "D:/newFuxios/tool/handeyecalui/handeyecalui/include"}, {"backtrace": 138, "path": "C:/opt/PCL/3rdParty/Eigen/eigen3"}, {"backtrace": 141, "path": "C:/opt/opencv/build/include"}, {"backtrace": 144, "path": "C:/opt/glog/include"}, {"backtrace": 147, "path": "C:/opt/aubo/include"}, {"backtrace": 148, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3"}, {"backtrace": 148, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12"}, {"backtrace": 148, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi"}, {"backtrace": 148, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp"}, {"backtrace": 148, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src"}, {"backtrace": 148, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src"}, {"backtrace": 148, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib"}, {"backtrace": 148, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include"}, {"backtrace": 148, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include"}, {"backtrace": 148, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext"}, {"backtrace": 5, "path": "D:/newFuxios/hardwaredriver/HikVisionCamera/include"}, {"backtrace": 5, "path": "C:/opt/hikvision/include"}, {"backtrace": 5, "path": "D:/newFuxios/hardwaredriver/AuboArcsDriver/include"}, {"backtrace": 5, "path": "C:/opt/auboarcs/include"}, {"backtrace": 5, "path": "D:/newFuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 149, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 150, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 150, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 150, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 151, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 151, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 152, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 153, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}, {"backtrace": 154, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts"}, {"backtrace": 155, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSerialPort"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1, 3, 5, 7]}], "dependencies": [{"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"backtrace": 5, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9"}, {"backtrace": 5, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "toolhandeyecaluihandeyecalui::@64c63141ea1fe7a116f6", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc -EHa -bigobj /MP -openmp /O1 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64                                     /INCREMENTAL:NO /subsystem:console /machine:x64", "role": "flags"}, {"backtrace": 5, "fragment": "..\\..\\..\\hardwaredriver\\HikVisionCamera\\MinSizeRel\\hardwaredriverHikVisionCamera.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\yaobi.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\pqp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\fcl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_qhull.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_csgjs.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assimp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_unzip.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_algorithms.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathplanners.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathoptimization.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_simulation.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_opengl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assembly.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_task.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_calibration.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_csg.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_control.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_proximitystrategies.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_core.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_common.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_math.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "opengl32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "glu32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_filesystem-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\..\\hardwaredriver\\AuboArcsDriver\\MinSizeRel\\hardwaredriverAuboArcsDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\..\\fuxicommon\\MinSizeRel\\fuxicommon.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sql.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Charts.lib", "role": "libraries"}, {"backtrace": 11, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5SerialPort.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 35, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sql.lib", "role": "libraries"}, {"backtrace": 37, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgets.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Gui.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Core.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\yaobi.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\pqp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\fcl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_qhull.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_csgjs.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assimp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_unzip.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_algorithms.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathplanners.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathoptimization.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_simulation.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_opengl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assembly.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_task.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_calibration.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_csg.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_control.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_proximitystrategies.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_core.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_common.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_math.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "opengl32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "glu32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_filesystem-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 58, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 62, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 64, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sql.lib", "role": "libraries"}, {"backtrace": 66, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgets.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Gui.lib", "role": "libraries"}, {"backtrace": 70, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Core.lib", "role": "libraries"}, {"backtrace": 75, "fragment": "C:\\opt\\auboarcs\\lib\\aubo_sdk.lib", "role": "libraries"}, {"backtrace": 75, "fragment": "C:\\opt\\auboarcs\\lib\\robot_proxy.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 75, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 75, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 87, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 75, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 75, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 75, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 75, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 91, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 75, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 75, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 93, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sql.lib", "role": "libraries"}, {"backtrace": 95, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgets.lib", "role": "libraries"}, {"backtrace": 97, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Gui.lib", "role": "libraries"}, {"backtrace": 99, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Core.lib", "role": "libraries"}, {"backtrace": 113, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 113, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 114, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 118, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 120, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgets.lib", "role": "libraries"}, {"backtrace": 122, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Gui.lib", "role": "libraries"}, {"backtrace": 124, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Core.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "advapi32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "toolhandeyecaluihandeyecalui", "nameOnDisk": "toolhandeyecaluihandeyecalui.exe", "paths": {"build": "tool/handeyecalui/handeyecalui", "source": "tool/handeyecalui/handeyecalui"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 3, 5, 7]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2, 4, 6]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/tool/handeyecalui/handeyecalui/toolhandeyecaluihandeyecalui_autogen/mocs_compilation_MinSizeRel.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecalui/handeyecalui/src/KalmanFilter3D.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/handeyecalui/handeyecalui/src/KalmanFilter3D.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecalui/handeyecalui/src/MainWindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/handeyecalui/handeyecalui/src/MainWindow.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecalui/handeyecalui/src/TrackingManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/handeyecalui/handeyecalui/src/TrackingManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecalui/handeyecalui/src/main.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}