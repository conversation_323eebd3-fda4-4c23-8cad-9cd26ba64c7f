{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "tool/communication/RelWithDebInfo/toolcommunication.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "add_unique_libraries", "add_definitions", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/library.cmake", "tool/communication/CMakeLists.txt", "builder/cmake/add_openssl.cmake", "builder/cmake/common.cmake", "builder/cmake/add_xerces-c.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_opencv.cmake", "builder/cmake/add_libevent.cmake", "builder/cmake/add_rttr.cmake", "builder/cmake/add_boost.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 76, "parent": 2}, {"command": 3, "file": 0, "line": 78, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 4}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 3, "parent": 6}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 2, "parent": 8}, {"command": 4, "file": 2, "line": 5, "parent": 9}, {"command": 5, "file": 0, "line": 68, "parent": 2}, {"command": 5, "file": 0, "line": 64, "parent": 2}, {"command": 5, "file": 0, "line": 67, "parent": 2}, {"command": 5, "file": 0, "line": 65, "parent": 2}, {"command": 3, "file": 0, "line": 84, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 15}, {"command": 5, "file": 0, "line": 66, "parent": 2}, {"command": 6, "file": 3, "line": 54, "parent": 7}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 4, "parent": 19}, {"command": 6, "file": 4, "line": 11, "parent": 20}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 5, "parent": 22}, {"command": 6, "file": 5, "line": 16, "parent": 23}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 6, "parent": 25}, {"command": 6, "file": 6, "line": 23, "parent": 26}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 7, "parent": 28}, {"command": 6, "file": 7, "line": 14, "parent": 29}, {"command": 6, "file": 2, "line": 23, "parent": 9}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 8, "parent": 32}, {"command": 6, "file": 8, "line": 29, "parent": 33}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 9, "parent": 35}, {"command": 6, "file": 9, "line": 40, "parent": 36}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 3, "file": 0, "line": 84, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 42}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -std:c++20 -MD"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 10, "define": "CPPHTTPLIB_OPENSSL_SUPPORT"}, {"backtrace": 11, "define": "QT_CORE_LIB"}, {"backtrace": 12, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 13, "define": "QT_GUI_LIB"}, {"backtrace": 14, "define": "QT_NO_DEBUG"}, {"backtrace": 16, "define": "QT_SQL_LIB"}, {"backtrace": 17, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 0, "path": "D:/newFuxios/build/tool/communication/toolcommunication_autogen/include_RelWithDebInfo"}, {"backtrace": 18, "path": "D:/newFuxios/tool/communication/include"}, {"backtrace": 21, "path": "C:/opt/xerces-c/include"}, {"backtrace": 24, "path": "C:/opt/glog/include"}, {"backtrace": 27, "path": "C:/opt/opencv/build/include"}, {"backtrace": 30, "path": "C:/opt/libevent/include"}, {"backtrace": 31, "path": "C:/opt/openssl/include"}, {"backtrace": 34, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 37, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 38, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 38, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 38, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 39, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 39, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 40, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 41, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [5, 43], "standard": "20"}, "sourceIndexes": [0, 1]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "toolcommunication::@116eb0f160f4d76de168", "name": "toolcommunication", "nameOnDisk": "toolcommunication.lib", "paths": {"build": "tool/communication", "source": "tool/communication"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/tool/communication/toolcommunication_autogen/mocs_compilation_RelWithDebInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/communication/src/HttpsServer.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/communication/include/HttpsServer.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}