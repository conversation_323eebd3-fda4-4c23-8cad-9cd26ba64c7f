{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "hardwaredriver/OpcDa/RelWithDebInfo/hardwaredriverOpcDa.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "add_unique_libraries", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/library.cmake", "hardwaredriver/OpcDa/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_rttr.cmake", "builder/cmake/add_boost.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 4, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 76, "parent": 2}, {"command": 3, "file": 0, "line": 78, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 4}, {"command": 4, "file": 0, "line": 68, "parent": 2}, {"command": 4, "file": 0, "line": 64, "parent": 2}, {"command": 4, "file": 0, "line": 67, "parent": 2}, {"command": 4, "file": 0, "line": 65, "parent": 2}, {"command": 4, "file": 0, "line": 66, "parent": 2}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 11}, {"command": 5, "file": 2, "line": 54, "parent": 12}, {"command": 1, "file": 2, "line": 81, "parent": 12}, {"file": 3, "parent": 14}, {"command": 5, "file": 3, "line": 16, "parent": 15}, {"command": 1, "file": 2, "line": 81, "parent": 12}, {"file": 4, "parent": 17}, {"command": 5, "file": 4, "line": 29, "parent": 18}, {"command": 1, "file": 2, "line": 81, "parent": 12}, {"file": 5, "parent": 20}, {"command": 5, "file": 5, "line": 40, "parent": 21}, {"command": 5, "file": 0, "line": 83, "parent": 2}, {"command": 5, "file": 0, "line": 83, "parent": 2}, {"command": 5, "file": 0, "line": 83, "parent": 2}, {"command": 5, "file": 0, "line": 83, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -std:c++20 -MD"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 6, "define": "QT_CORE_LIB"}, {"backtrace": 7, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 8, "define": "QT_GUI_LIB"}, {"backtrace": 9, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 10, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 0, "path": "D:/newFuxios/build/hardwaredriver/OpcDa/hardwaredriverOpcDa_autogen/include_RelWithDebInfo"}, {"backtrace": 13, "path": "D:/newFuxios/hardwaredriver/OpcDa/include"}, {"backtrace": 16, "path": "C:/opt/glog/include"}, {"backtrace": 5, "path": "D:/newFuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 19, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 22, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 23, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 23, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 23, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 24, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 24, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 25, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 26, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1]}], "dependencies": [{"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "hardwaredriverOpcDa::@a2142d788288f069154a", "name": "hardwaredriverOpcDa", "nameOnDisk": "hardwaredriverOpcDa.lib", "paths": {"build": "hardwaredriver/OpcDa", "source": "hardwaredriver/OpcDa"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/hardwaredriver/OpcDa/hardwaredriverOpcDa_autogen/mocs_compilation_RelWithDebInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "hardwaredriver/OpcDa/src/OPCDAClientSync.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "hardwaredriver/OpcDa/include/OPCDAClientSync.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}