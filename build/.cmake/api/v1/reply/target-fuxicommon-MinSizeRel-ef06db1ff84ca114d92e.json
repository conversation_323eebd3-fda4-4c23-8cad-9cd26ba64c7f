{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "fuxicommon/MinSizeRel/fuxicommon.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "add_unique_libraries", "add_definitions", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/library.cmake", "fuxicommon/CMakeLists.txt", "builder/cmake/add_openssl.cmake", "builder/cmake/common.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_xerces-c.cmake", "builder/cmake/add_opencv.cmake", "builder/cmake/add_rttr.cmake", "builder/cmake/add_boost.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 76, "parent": 2}, {"command": 3, "file": 0, "line": 78, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 4}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 3, "parent": 6}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 2, "parent": 8}, {"command": 4, "file": 2, "line": 5, "parent": 9}, {"command": 5, "file": 0, "line": 68, "parent": 2}, {"command": 5, "file": 0, "line": 64, "parent": 2}, {"command": 5, "file": 0, "line": 67, "parent": 2}, {"command": 5, "file": 0, "line": 65, "parent": 2}, {"command": 3, "file": 0, "line": 84, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 15}, {"command": 5, "file": 0, "line": 66, "parent": 2}, {"command": 6, "file": 3, "line": 54, "parent": 7}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 4, "parent": 19}, {"command": 6, "file": 4, "line": 16, "parent": 20}, {"command": 6, "file": 2, "line": 23, "parent": 9}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 5, "parent": 23}, {"command": 6, "file": 5, "line": 11, "parent": 24}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 6, "parent": 26}, {"command": 6, "file": 6, "line": 23, "parent": 27}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 7, "parent": 29}, {"command": 6, "file": 7, "line": 29, "parent": 30}, {"command": 1, "file": 3, "line": 81, "parent": 7}, {"file": 8, "parent": 32}, {"command": 6, "file": 8, "line": 40, "parent": 33}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 6, "file": 0, "line": 83, "parent": 2}, {"command": 3, "file": 0, "line": 84, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 39}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O1 /Ob1 /DNDEBUG -std:c++20 -MD"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 10, "define": "CPPHTTPLIB_OPENSSL_SUPPORT"}, {"backtrace": 11, "define": "QT_CORE_LIB"}, {"backtrace": 12, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 13, "define": "QT_GUI_LIB"}, {"backtrace": 14, "define": "QT_NO_DEBUG"}, {"backtrace": 16, "define": "QT_SQL_LIB"}, {"backtrace": 17, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 0, "path": "D:/newFuxios/build/fuxicommon/fuxicommon_autogen/include_MinSizeRel"}, {"backtrace": 18, "path": "D:/newFuxios/fuxicommon/include"}, {"backtrace": 21, "path": "C:/opt/glog/include"}, {"backtrace": 22, "path": "C:/opt/openssl/include"}, {"backtrace": 25, "path": "C:/opt/xerces-c/include"}, {"backtrace": 28, "path": "C:/opt/opencv/build/include"}, {"backtrace": 31, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 34, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 35, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 35, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 35, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 36, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 36, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 37, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 38, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [5, 40], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 13, 14, 15, 17, 18, 19, 55, 58, 59, 60, 61]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /O1 /Ob1 /DNDEBUG -MD"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 10, "define": "CPPHTTPLIB_OPENSSL_SUPPORT"}, {"backtrace": 11, "define": "QT_CORE_LIB"}, {"backtrace": 12, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 13, "define": "QT_GUI_LIB"}, {"backtrace": 14, "define": "QT_NO_DEBUG"}, {"backtrace": 16, "define": "QT_SQL_LIB"}, {"backtrace": 17, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 0, "path": "D:/newFuxios/build/fuxicommon/fuxicommon_autogen/include_MinSizeRel"}, {"backtrace": 18, "path": "D:/newFuxios/fuxicommon/include"}, {"backtrace": 21, "path": "C:/opt/glog/include"}, {"backtrace": 22, "path": "C:/opt/openssl/include"}, {"backtrace": 25, "path": "C:/opt/xerces-c/include"}, {"backtrace": 28, "path": "C:/opt/opencv/build/include"}, {"backtrace": 31, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 34, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 35, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 35, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 35, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 36, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 36, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 37, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 38, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}], "language": "C", "sourceIndexes": [56, 57]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "fuxicommon::@58335e9a86196d0a97e7", "name": "fuxicommon", "nameOnDisk": "fuxicommon.lib", "paths": {"build": "fuxicommon", "source": "fuxicommon"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 13, 14, 15, 17, 18, 19, 55, 56, 57, 58, 59, 60, 61]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [10, 12, 16, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/fuxicommon/fuxicommon_autogen/mocs_compilation_MinSizeRel.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/EventListenerSupport.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/Executor.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/ExecutorContenxt.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/FileUtil.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/database/QDataBaseUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/database/QSqlUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/filemanager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/json/JSON.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/json/from_json.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "fuxicommon/src/json/from_json.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/json/to_json.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "fuxicommon/src/json/to_json.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/model/QRTTRItemModel.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/model/QRTTRTableModel.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/parser/CSVParserImpl.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "fuxicommon/src/parser/CSVParserImpl.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/parser/FileIOOperation.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/parser/NumberUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/parser/StringTransform.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/allocators.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/document.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/encodedstream.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/encodings.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/error/en.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/error/error.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/filereadstream.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/filewritestream.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/fwd.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/internal/biginteger.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/internal/diyfp.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/internal/dtoa.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/internal/ieee754.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/internal/itoa.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/internal/meta.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/internal/pow10.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/internal/regex.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/internal/stack.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/internal/strfunc.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/internal/strtod.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/internal/swap.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/istreamwrapper.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/memorybuffer.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/memorystream.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/msinttypes/inttypes.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/msinttypes/stdint.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/ostreamwrapper.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/pointer.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/prettywriter.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/rapidjson.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/reader.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/schema.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/stream.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/stringbuffer.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/src/parser/rapidjson/writer.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/serialAlgorithm.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 1, "path": "fuxicommon/src/shell.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 1, "path": "fuxicommon/src/sqlite3.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/stringAlgorithm.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/timer.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/tinyxml2.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "fuxicommon/src/xml/XML.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "fuxicommon/include/EventListenerSupport.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/Executor.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/ExecutorContenxt.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/FileUtil.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/JSON.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/QDataBaseUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/QRTTRItemModel.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/QRTTRTableModel.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/QSqlUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/XML.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/filemanager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/glog.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/httplib.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/nlohmann/json.hpp", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/nlohmann/json_fwd.hpp", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/parser/CSVParser.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/parser/FileIOOperation.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/parser/NumberUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/parser/StringTransform.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/serialAlgorithm.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/sqlite3.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/sqlite3ext.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/stringAlgorithm.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/timer.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "fuxicommon/include/tinyxml2.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}