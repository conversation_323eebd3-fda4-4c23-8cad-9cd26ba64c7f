{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "hardwaredriver/agilerobotDriver/Release/hardwaredriveragilerobotDriver.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "add_unique_libraries", "add_definitions", "find_package", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/library.cmake", "hardwaredriver/agilerobotDriver/CMakeLists.txt", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkConfig.cmake", "builder/cmake/add_robwork.cmake", "builder/cmake/common.cmake", "builder/cmake/add_eigen.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_agilerobot.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 76, "parent": 2}, {"command": 3, "file": 0, "line": 78, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 4}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 4, "parent": 6}, {"command": 1, "file": 4, "line": 81, "parent": 7}, {"file": 3, "parent": 8}, {"command": 5, "file": 3, "line": 18, "parent": 9}, {"file": 2, "parent": 10}, {"command": 4, "file": 2, "line": 310, "parent": 11}, {"command": 6, "file": 0, "line": 68, "parent": 2}, {"command": 6, "file": 0, "line": 64, "parent": 2}, {"command": 6, "file": 0, "line": 67, "parent": 2}, {"command": 6, "file": 0, "line": 65, "parent": 2}, {"command": 6, "file": 0, "line": 66, "parent": 2}, {"command": 7, "file": 4, "line": 54, "parent": 7}, {"command": 1, "file": 4, "line": 81, "parent": 7}, {"file": 5, "parent": 19}, {"command": 7, "file": 5, "line": 30, "parent": 20}, {"command": 1, "file": 4, "line": 81, "parent": 7}, {"file": 6, "parent": 22}, {"command": 7, "file": 6, "line": 16, "parent": 23}, {"command": 7, "file": 3, "line": 21, "parent": 9}, {"command": 1, "file": 4, "line": 81, "parent": 7}, {"file": 7, "parent": 26}, {"command": 7, "file": 7, "line": 12, "parent": 27}, {"command": 7, "file": 0, "line": 83, "parent": 2}, {"command": 7, "file": 0, "line": 83, "parent": 2}, {"command": 7, "file": 0, "line": 83, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc -EHa -bigobj /MP -openmp /O2 /Ob2 /DNDEBUG -std:c++20 -MD"}], "defines": [{"backtrace": 12, "define": "BIND_FORTRAN_LOWERCASE_UNDERSCORE"}, {"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 12, "define": "MSVC_AMD64"}, {"backtrace": 12, "define": "NOMINMAX"}, {"backtrace": 13, "define": "QT_CORE_LIB"}, {"backtrace": 14, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 15, "define": "QT_GUI_LIB"}, {"backtrace": 16, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 17, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}, {"backtrace": 12, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 12, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 12, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 12, "define": "_SCL_SECURE_NO_WARNINGS"}, {"backtrace": 12, "define": "_WIN32_WINNT=0x0501"}], "includes": [{"backtrace": 0, "path": "D:/newFuxios/build/hardwaredriver/agilerobotDriver/hardwaredriveragilerobotDriver_autogen/include_Release"}, {"backtrace": 18, "path": "D:/newFuxios/hardwaredriver/agilerobotDriver/include"}, {"backtrace": 21, "path": "C:/opt/PCL/3rdParty/Eigen/eigen3"}, {"backtrace": 24, "path": "C:/opt/glog/include"}, {"backtrace": 25, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3"}, {"backtrace": 25, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12"}, {"backtrace": 25, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi"}, {"backtrace": 25, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp"}, {"backtrace": 25, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src"}, {"backtrace": 25, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src"}, {"backtrace": 25, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib"}, {"backtrace": 25, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include"}, {"backtrace": 25, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include"}, {"backtrace": 25, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext"}, {"backtrace": 28, "path": "C:/opt/<PERSON><PERSON>/include"}, {"backtrace": 5, "path": "D:/newFuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 25, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 29, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 29, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 29, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 30, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 30, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 31, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1]}], "dependencies": [{"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "hardwaredriveragilerobotDriver::@14914dfd89874674d41d", "name": "hardwaredriveragilerobotDriver", "nameOnDisk": "hardwaredriveragilerobotDriver.lib", "paths": {"build": "hardwaredriver/agilerobotDriver", "source": "hardwaredriver/agilerobotDriver"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/hardwaredriver/agilerobotDriver/hardwaredriveragilerobotDriver_autogen/mocs_compilation_Release.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "hardwaredriver/agilerobotDriver/src/agilerobotDriver.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "hardwaredriver/agilerobotDriver/include/agilerobotDriver.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}