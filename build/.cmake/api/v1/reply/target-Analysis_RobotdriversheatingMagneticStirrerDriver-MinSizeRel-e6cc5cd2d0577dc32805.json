{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "Analysis_Robot/drivers/heatingMagneticStirrerDriver/MinSizeRel/Analysis_RobotdriversheatingMagneticStirrerDriver.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "add_unique_libraries", "include_directories"], "files": ["builder/cmake/library.cmake", "Analysis_Robot/drivers/heatingMagneticStirrerDriver/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_libmodbus.cmake", "builder/cmake/add_boost.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 2, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 88, "parent": 2}, {"command": 3, "file": 0, "line": 90, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 4}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 6}, {"command": 4, "file": 2, "line": 54, "parent": 7}, {"command": 1, "file": 2, "line": 81, "parent": 7}, {"file": 3, "parent": 9}, {"command": 4, "file": 3, "line": 16, "parent": 10}, {"command": 1, "file": 2, "line": 81, "parent": 7}, {"file": 4, "parent": 12}, {"command": 4, "file": 4, "line": 8, "parent": 13}, {"command": 1, "file": 2, "line": 81, "parent": 7}, {"file": 5, "parent": 15}, {"command": 4, "file": 5, "line": 40, "parent": 16}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O1 /Ob1 /DNDEBUG -std:c++20 -MD"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 5, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 8, "path": "D:/newFuxios/Analysis_Robot/drivers/heatingMagneticStirrerDriver/include"}, {"backtrace": 11, "path": "C:/opt/glog/include"}, {"backtrace": 14, "path": "C:/opt/libmodbus/include"}, {"backtrace": 5, "path": "D:/newFuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 17, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "Analysis_RobotdriversheatingMagneticStirrerDriver::@e9ece92fe2bc47be420b", "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "nameOnDisk": "Analysis_RobotdriversheatingMagneticStirrerDriver.lib", "paths": {"build": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "source": "Analysis_Robot/drivers/heatingMagneticStirrerDriver"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/drivers/heatingMagneticStirrerDriver/src/HeatingMagneticStirrerDriver.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "Analysis_Robot/drivers/heatingMagneticStirrerDriver/include/HeatingMagneticStirrerDriver.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}