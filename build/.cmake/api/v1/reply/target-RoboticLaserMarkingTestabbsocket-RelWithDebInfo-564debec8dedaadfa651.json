{"artifacts": [{"path": "D:/newFuxios/install/x64-install/dev/bin/RelWithDebInfo/RoboticLaserMarkingTestabbsocket.exe"}, {"path": "D:/newFuxios/install/x64-install/dev/bin/RelWithDebInfo/RoboticLaserMarkingTestabbsocket.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/executable.cmake", "RoboticLaserMarking/Test/abbsocket/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/library.cmake", "RoboticLaserMarking/laserDriver/CMakeLists.txt", "builder/cmake/add_eigen.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_libmodbus.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 26, "parent": 2}, {"command": 3, "file": 0, "line": 30, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 6}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 8}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 10}, {"command": 2, "file": 0, "line": 46, "parent": 2}, {"file": 4}, {"command": 1, "file": 4, "line": 2, "parent": 13}, {"file": 3, "parent": 14}, {"command": 3, "file": 3, "line": 90, "parent": 15}, {"command": 2, "file": 3, "line": 53, "parent": 16}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 18}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 20}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 22}, {"command": 4, "file": 0, "line": 17, "parent": 2}, {"command": 4, "file": 0, "line": 13, "parent": 2}, {"command": 4, "file": 0, "line": 16, "parent": 2}, {"command": 4, "file": 0, "line": 14, "parent": 2}, {"command": 4, "file": 0, "line": 15, "parent": 2}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 29}, {"command": 5, "file": 2, "line": 54, "parent": 30}, {"command": 1, "file": 2, "line": 81, "parent": 30}, {"file": 5, "parent": 32}, {"command": 5, "file": 5, "line": 30, "parent": 33}, {"command": 1, "file": 2, "line": 81, "parent": 30}, {"file": 6, "parent": 35}, {"command": 5, "file": 6, "line": 16, "parent": 36}, {"command": 1, "file": 2, "line": 81, "parent": 30}, {"file": 7, "parent": 38}, {"command": 5, "file": 7, "line": 8, "parent": 39}, {"command": 5, "file": 0, "line": 34, "parent": 2}, {"command": 5, "file": 0, "line": 34, "parent": 2}, {"command": 5, "file": 0, "line": 34, "parent": 2}, {"command": 5, "file": 0, "line": 34, "parent": 2}, {"command": 5, "file": 0, "line": 34, "parent": 2}, {"command": 5, "file": 0, "line": 34, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -std:c++20 -MD"}], "defines": [{"backtrace": 9, "define": "QT_CHARTS_LIB"}, {"backtrace": 24, "define": "QT_CORE_LIB"}, {"backtrace": 25, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 26, "define": "QT_GUI_LIB"}, {"backtrace": 11, "define": "QT_NETWORK_LIB"}, {"backtrace": 27, "define": "QT_NO_DEBUG"}, {"backtrace": 7, "define": "QT_SQL_LIB"}, {"backtrace": 28, "define": "QT_WIDGETS_LIB"}], "includes": [{"backtrace": 0, "path": "D:/newFuxios/build/RoboticLaserMarking/Test/abbsocket/RoboticLaserMarkingTestabbsocket_autogen/include_RelWithDebInfo"}, {"backtrace": 31, "path": "D:/newFuxios/RoboticLaserMarking/Test/abbsocket/include"}, {"backtrace": 34, "path": "C:/opt/PCL/3rdParty/Eigen/eigen3"}, {"backtrace": 37, "path": "C:/opt/glog/include"}, {"backtrace": 40, "path": "C:/opt/libmodbus/include"}, {"backtrace": 5, "path": "D:/newFuxios/RoboticLaserMarking/RFIDDriver/include"}, {"backtrace": 5, "path": "D:/newFuxios/RoboticLaserMarking/laserDriver/include"}, {"backtrace": 5, "path": "C:/opt/laserControlFrame/include"}, {"backtrace": 5, "path": "D:/newFuxios/RoboticLaserMarking/AbbDriver/include"}, {"backtrace": 41, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 41, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 41, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 42, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 42, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 43, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 44, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}, {"backtrace": 45, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts"}, {"backtrace": 46, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtNetwork"}], "language": "CXX", "languageStandard": {"backtraces": [23], "standard": "20"}, "sourceIndexes": [0, 1]}], "dependencies": [{"backtrace": 5, "id": "RoboticLaserMarkingAbbDriver::@9c3ce50b53cc3ff6eff5"}, {"backtrace": 5, "id": "RoboticLaserMarkingRFIDDriver::@d1520424919af3a40272"}, {"backtrace": 5, "id": "RoboticLaserMarkinglaserDriver::@cf8a855e37e415d7ca08"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "RoboticLaserMarkingTestabbsocket::@bca145e2342aef659032", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64                                     /debug /INCREMENTAL /subsystem:console /machine:x64", "role": "flags"}, {"backtrace": 5, "fragment": "..\\..\\RFIDDriver\\RelWithDebInfo\\RoboticLaserMarkingRFIDDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\laserDriver\\RelWithDebInfo\\RoboticLaserMarkinglaserDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\AbbDriver\\RelWithDebInfo\\RoboticLaserMarkingAbbDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sql.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Charts.lib", "role": "libraries"}, {"backtrace": 11, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Network.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\laserControlFrame\\lib\\LaserControlFrameSDK.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 19, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgets.lib", "role": "libraries"}, {"backtrace": 21, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Gui.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Core.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "RoboticLaserMarkingTestabbsocket", "nameOnDisk": "RoboticLaserMarkingTestabbsocket.exe", "paths": {"build": "RoboticLaserMarking/Test/abbsocket", "source": "RoboticLaserMarking/Test/abbsocket"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/RoboticLaserMarking/Test/abbsocket/RoboticLaserMarkingTestabbsocket_autogen/mocs_compilation_RelWithDebInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/Test/abbsocket/src/abbsocket.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}