{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "MJServer_Refactor/Library/RelWithDebInfo/MJServer_RefactorLibrary.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "add_unique_libraries", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/library.cmake", "MJServer_Refactor/Library/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/add_libmodbus.cmake", "builder/cmake/add_jsoncpp.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 76, "parent": 2}, {"command": 3, "file": 0, "line": 78, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 4}, {"command": 4, "file": 0, "line": 68, "parent": 2}, {"command": 4, "file": 0, "line": 64, "parent": 2}, {"command": 4, "file": 0, "line": 67, "parent": 2}, {"command": 4, "file": 0, "line": 65, "parent": 2}, {"command": 4, "file": 0, "line": 66, "parent": 2}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 11}, {"command": 5, "file": 2, "line": 54, "parent": 12}, {"command": 1, "file": 2, "line": 81, "parent": 12}, {"file": 3, "parent": 14}, {"command": 5, "file": 3, "line": 8, "parent": 15}, {"command": 1, "file": 2, "line": 81, "parent": 12}, {"file": 4, "parent": 17}, {"command": 5, "file": 4, "line": 10, "parent": 18}, {"command": 5, "file": 0, "line": 83, "parent": 2}, {"command": 5, "file": 0, "line": 83, "parent": 2}, {"command": 5, "file": 0, "line": 83, "parent": 2}, {"command": 5, "file": 0, "line": 83, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -std:c++20 -MD"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 6, "define": "QT_CORE_LIB"}, {"backtrace": 7, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 8, "define": "QT_GUI_LIB"}, {"backtrace": 9, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 10, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 0, "path": "D:/newFuxios/build/MJServer_Refactor/Library/MJServer_RefactorLibrary_autogen/include_RelWithDebInfo"}, {"backtrace": 13, "path": "D:/newFuxios/MJServer_Refactor/Library/include"}, {"backtrace": 16, "path": "C:/opt/libmodbus/include"}, {"backtrace": 19, "path": "C:/opt/jsoncpp/include"}, {"backtrace": 5, "path": "D:/newFuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 5, "path": "C:/opt/glog/include"}, {"backtrace": 20, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 20, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 20, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 21, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 21, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 22, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 23, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}], "dependencies": [{"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "MJServer_RefactorLibrary::@8670365571700e12b583", "name": "MJServer_RefactorLibrary", "nameOnDisk": "MJServer_RefactorLibrary.lib", "paths": {"build": "MJServer_Refactor/Library", "source": "MJServer_Refactor/Library"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/MJServer_Refactor/Library/MJServer_RefactorLibrary_autogen/mocs_compilation_RelWithDebInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "MJServer_Refactor/Library/src/abb_direct_client.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "MJServer_Refactor/Library/src/config_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "MJServer_Refactor/Library/src/control_system.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "MJServer_Refactor/Library/src/feeder_client.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "MJServer_Refactor/Library/src/feeder_manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "MJServer_Refactor/Library/src/mes_server.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "MJServer_Refactor/Library/src/system_adapter.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "MJServer_Refactor/Library/src/weight_calculator.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "MJServer_Refactor/Library/include/abb_direct_client.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "MJServer_Refactor/Library/include/abb_protocol.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "MJServer_Refactor/Library/include/config_manager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "MJServer_Refactor/Library/include/connection_pool.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "MJServer_Refactor/Library/include/control_system.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "MJServer_Refactor/Library/include/data_structures.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "MJServer_Refactor/Library/include/feeder_client.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "MJServer_Refactor/Library/include/feeder_manager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "MJServer_Refactor/Library/include/mes_server.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "MJServer_Refactor/Library/include/system_adapter.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "MJServer_Refactor/Library/include/weight_calculator.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}