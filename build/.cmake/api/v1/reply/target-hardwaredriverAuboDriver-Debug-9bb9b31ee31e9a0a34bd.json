{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "hardwaredriver/AuboDriver/Debug/hardwaredriverAuboDriver.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "add_definitions", "find_package", "target_link_libraries", "add_unique_libraries", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/library.cmake", "hardwaredriver/AuboDriver/CMakeLists.txt", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkConfig.cmake", "builder/cmake/add_robwork.cmake", "builder/cmake/common.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_aubo.cmake", "builder/cmake/add_rttr.cmake", "builder/cmake/add_boost.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 76, "parent": 2}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 4, "parent": 4}, {"command": 1, "file": 4, "line": 81, "parent": 5}, {"file": 3, "parent": 6}, {"command": 3, "file": 3, "line": 18, "parent": 7}, {"file": 2, "parent": 8}, {"command": 2, "file": 2, "line": 310, "parent": 9}, {"command": 5, "file": 0, "line": 78, "parent": 2}, {"command": 4, "file": 0, "line": 53, "parent": 11}, {"command": 6, "file": 0, "line": 68, "parent": 2}, {"command": 6, "file": 0, "line": 64, "parent": 2}, {"command": 6, "file": 0, "line": 67, "parent": 2}, {"command": 6, "file": 0, "line": 65, "parent": 2}, {"command": 5, "file": 0, "line": 84, "parent": 2}, {"command": 4, "file": 0, "line": 53, "parent": 17}, {"command": 6, "file": 0, "line": 66, "parent": 2}, {"command": 7, "file": 4, "line": 54, "parent": 5}, {"command": 1, "file": 4, "line": 81, "parent": 5}, {"file": 5, "parent": 21}, {"command": 7, "file": 5, "line": 16, "parent": 22}, {"command": 1, "file": 4, "line": 81, "parent": 5}, {"file": 6, "parent": 24}, {"command": 7, "file": 6, "line": 8, "parent": 25}, {"command": 7, "file": 3, "line": 21, "parent": 7}, {"command": 1, "file": 4, "line": 81, "parent": 5}, {"file": 7, "parent": 28}, {"command": 7, "file": 7, "line": 29, "parent": 29}, {"command": 1, "file": 4, "line": 81, "parent": 5}, {"file": 8, "parent": 31}, {"command": 7, "file": 8, "line": 40, "parent": 32}, {"command": 7, "file": 0, "line": 83, "parent": 2}, {"command": 7, "file": 0, "line": 83, "parent": 2}, {"command": 7, "file": 0, "line": 83, "parent": 2}, {"command": 7, "file": 0, "line": 83, "parent": 2}, {"command": 5, "file": 0, "line": 84, "parent": 2}, {"command": 4, "file": 0, "line": 53, "parent": 38}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc -EHa -bigobj /MP -openmp /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd"}], "defines": [{"backtrace": 10, "define": "BIND_FORTRAN_LOWERCASE_UNDERSCORE"}, {"backtrace": 12, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 12, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 12, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 12, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 12, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 12, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 12, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 12, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 12, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 10, "define": "MSVC_AMD64"}, {"backtrace": 10, "define": "NOMINMAX"}, {"backtrace": 13, "define": "QT_CORE_LIB"}, {"backtrace": 14, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 15, "define": "QT_GUI_LIB"}, {"backtrace": 16, "define": "QT_NO_DEBUG"}, {"backtrace": 18, "define": "QT_SQL_LIB"}, {"backtrace": 19, "define": "QT_WIDGETS_LIB"}, {"backtrace": 12, "define": "RTTR_DLL"}, {"backtrace": 10, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 10, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 10, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 10, "define": "_SCL_SECURE_NO_WARNINGS"}, {"backtrace": 10, "define": "_WIN32_WINNT=0x0501"}], "includes": [{"backtrace": 0, "path": "D:/newFuxios/build/hardwaredriver/AuboDriver/hardwaredriverAuboDriver_autogen/include_Debug"}, {"backtrace": 20, "path": "D:/newFuxios/hardwaredriver/AuboDriver/include"}, {"backtrace": 23, "path": "C:/opt/glog/include"}, {"backtrace": 26, "path": "C:/opt/aubo/include"}, {"backtrace": 27, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3"}, {"backtrace": 27, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12"}, {"backtrace": 27, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi"}, {"backtrace": 27, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp"}, {"backtrace": 27, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src"}, {"backtrace": 27, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src"}, {"backtrace": 27, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib"}, {"backtrace": 27, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include"}, {"backtrace": 27, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include"}, {"backtrace": 27, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext"}, {"backtrace": 30, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 33, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 34, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 34, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 34, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 35, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 35, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 36, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 37, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [12, 39], "standard": "20"}, "sourceIndexes": [0, 1, 2]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "hardwaredriverAuboDriver::@e75f830eb736a5dca1ce", "name": "hardwaredriverAuboDriver", "nameOnDisk": "hardwaredriverAuboDriver.lib", "paths": {"build": "hardwaredriver/AuboDriver", "source": "hardwaredriver/AuboDriver"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3, 4, 5]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/hardwaredriver/AuboDriver/hardwaredriverAuboDriver_autogen/mocs_compilation_Debug.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "hardwaredriver/AuboDriver/src/AuboRobot.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "hardwaredriver/AuboDriver/src/stdafx.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "hardwaredriver/AuboDriver/include/AuboRobot.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "hardwaredriver/AuboDriver/include/stdafx.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "hardwaredriver/AuboDriver/include/targetver.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}