{"backtraceGraph": {"commands": ["install", "include"], "files": ["builder/cmake/library.cmake", "hardwaredriver/AuboArcsDriver/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 110, "parent": 2}, {"command": 0, "file": 0, "line": 114, "parent": 2}]}, "installers": [{"backtrace": 3, "component": "include", "destination": "D:/newFuxios/install/x64-install/dev//", "paths": ["hardwaredriver/AuboArcsDriver/include"], "type": "directory"}, {"backtrace": 4, "component": "Unspecified", "destination": "D:/newFuxios/build/../install/x64-install/dev/lib/pkgconfig", "paths": ["build/hardwaredriverAuboArcsDriver.pc"], "type": "file"}], "paths": {"build": "hardwaredriver/AuboArcsDriver", "source": "hardwaredriver/AuboArcsDriver"}}