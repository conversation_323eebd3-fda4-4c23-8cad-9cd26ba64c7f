{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["tool/handeyecaltest/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "toolhandeyecaltest::@ae279e4383f26a866133"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "ALL_BUILD::@ae279e4383f26a866133", "isGeneratorProvided": true, "name": "ALL_BUILD", "paths": {"build": "tool/handeyecaltest", "source": "tool/handeyecaltest"}, "sources": [], "type": "UTILITY"}