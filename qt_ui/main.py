#!/usr/bin/env python3
"""
邮箱验证码管理系统 - Qt GUI主程序
"""

import sys
import os
from pathlib import Path

# 添加父目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from PyQt5.QtWidgets import QApplication, QMainWindow, QSystemTrayIcon, QMenu, QAction
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap
import requests

from email_verification_ui import EmailVerificationWidget

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.api_base = "http://localhost:8000"
        self.init_ui()
        self.init_system_tray()
        self.init_timer()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("邮箱验证码管理系统 - 616866.xyz")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置中央widget
        self.central_widget = EmailVerificationWidget(self.api_base)
        self.setCentralWidget(self.central_widget)
        
        # 设置窗口图标
        self.setWindowIcon(self.create_icon())
        
    def init_system_tray(self):
        """初始化系统托盘"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self)
            self.tray_icon.setIcon(self.create_icon())
            
            # 创建托盘菜单
            tray_menu = QMenu()
            
            show_action = QAction("显示主窗口", self)
            show_action.triggered.connect(self.show)
            tray_menu.addAction(show_action)
            
            tray_menu.addSeparator()
            
            quit_action = QAction("退出", self)
            quit_action.triggered.connect(QApplication.instance().quit)
            tray_menu.addAction(quit_action)
            
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.show()
            
            # 双击托盘图标显示窗口
            self.tray_icon.activated.connect(self.tray_icon_activated)
    
    def init_timer(self):
        """初始化定时器"""
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # 每5秒更新一次状态
        
    def create_icon(self):
        """创建应用图标"""
        # 创建一个简单的图标
        pixmap = QPixmap(32, 32)
        pixmap.fill()
        return QIcon(pixmap)
    
    def tray_icon_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show()
            self.raise_()
            self.activateWindow()
    
    def update_status(self):
        """更新系统状态"""
        try:
            response = requests.get(f"{self.api_base}/health", timeout=2)
            if response.status_code == 200:
                self.central_widget.update_connection_status(True)
            else:
                self.central_widget.update_connection_status(False)
        except:
            self.central_widget.update_connection_status(False)
    
    def closeEvent(self, event):
        """关闭事件处理"""
        if QSystemTrayIcon.isSystemTrayAvailable() and self.tray_icon.isVisible():
            self.hide()
            event.ignore()
        else:
            event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)  # 关闭窗口不退出程序
    
    # 设置应用信息
    app.setApplicationName("邮箱验证码管理系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("616866.xyz")
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
