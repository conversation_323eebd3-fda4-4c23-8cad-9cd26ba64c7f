/* 邮箱验证码管理系统 - Qt样式表 */

/* 主窗口样式 */
QMainWindow {
    background-color: #f5f5f5;
}

/* 标签页样式 */
QTabWidget::pane {
    border: 1px solid #c0c0c0;
    background-color: white;
}

QTabWidget::tab-bar {
    left: 5px;
}

QTabBar::tab {
    background-color: #e1e1e1;
    border: 1px solid #c0c0c0;
    border-bottom-color: #c0c0c0;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    min-width: 8ex;
    padding: 8px 16px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom-color: white;
}

QTabBar::tab:hover {
    background-color: #f0f0f0;
}

/* 按钮样式 */
QPushButton {
    background-color: #007bff;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #0056b3;
}

QPushButton:pressed {
    background-color: #004085;
}

QPushButton:disabled {
    background-color: #6c757d;
}

/* 输入框样式 */
QLineEdit, QSpinBox {
    border: 2px solid #ced4da;
    border-radius: 4px;
    padding: 8px;
    font-size: 14px;
}

QLineEdit:focus, QSpinBox:focus {
    border-color: #007bff;
    outline: none;
}

/* 文本编辑器样式 */
QTextEdit {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px;
    background-color: white;
}

/* 表格样式 */
QTableWidget {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: white;
    gridline-color: #dee2e6;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #dee2e6;
}

QTableWidget::item:selected {
    background-color: #007bff;
    color: white;
}

QHeaderView::section {
    background-color: #f8f9fa;
    padding: 8px;
    border: 1px solid #dee2e6;
    font-weight: bold;
}

/* 分组框样式 */
QGroupBox {
    font-weight: bold;
    border: 2px solid #ced4da;
    border-radius: 8px;
    margin-top: 1ex;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: #495057;
}

/* 复选框样式 */
QCheckBox {
    spacing: 5px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
}

QCheckBox::indicator:unchecked {
    border: 2px solid #ced4da;
    border-radius: 3px;
    background-color: white;
}

QCheckBox::indicator:checked {
    border: 2px solid #007bff;
    border-radius: 3px;
    background-color: #007bff;
    image: url(checkmark.png);
}

/* 下拉框样式 */
QComboBox {
    border: 2px solid #ced4da;
    border-radius: 4px;
    padding: 8px;
    min-width: 6em;
}

QComboBox:focus {
    border-color: #007bff;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 15px;
    border-left-width: 1px;
    border-left-color: #ced4da;
    border-left-style: solid;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

/* 状态栏样式 */
QFrame[frameShape="4"] {
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px;
}

/* 标签样式 */
QLabel {
    color: #495057;
}

/* 滚动条样式 */
QScrollBar:vertical {
    border: none;
    background: #f8f9fa;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background: #ced4da;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: #adb5bd;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

/* 进度条样式 */
QProgressBar {
    border: 2px solid #ced4da;
    border-radius: 5px;
    text-align: center;
}

QProgressBar::chunk {
    background-color: #28a745;
    border-radius: 3px;
}
