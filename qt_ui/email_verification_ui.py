#!/usr/bin/env python3
"""
邮箱验证码管理系统 - 主界面组件
"""

import json
import requests
from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QTextEdit, QTableWidget, QTableWidgetItem,
    QTabWidget, QGroupBox, QComboBox, QCheckBox, QSpinBox,
    QMessageBox, QProgressBar, QSplitter, QFrame
)
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QPalette, QColor

class EmailVerificationWidget(QWidget):
    """邮箱验证码管理主界面"""
    
    def __init__(self, api_base):
        super().__init__()
        self.api_base = api_base
        self.init_ui()
        self.load_emails()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 状态栏
        self.create_status_bar(layout)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 邮箱生成标签页
        self.tab_widget.addTab(self.create_generate_tab(), "📧 生成邮箱")
        
        # 验证码查询标签页
        self.tab_widget.addTab(self.create_code_tab(), "🔐 验证码查询")
        
        # 邮箱管理标签页
        self.tab_widget.addTab(self.create_manage_tab(), "📋 邮箱管理")
        
        # 系统监控标签页
        self.tab_widget.addTab(self.create_monitor_tab(), "📊 系统监控")
        
        layout.addWidget(self.tab_widget)
        self.setLayout(layout)
        
    def create_status_bar(self, layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_layout = QHBoxLayout()
        
        # 连接状态
        self.connection_label = QLabel("🔴 API服务器: 未连接")
        status_layout.addWidget(self.connection_label)
        
        status_layout.addStretch()
        
        # 域名信息
        domain_label = QLabel("🌐 域名: 616866.xyz")
        status_layout.addWidget(domain_label)
        
        # Gmail状态
        self.gmail_label = QLabel("📧 Gmail: 未知")
        status_layout.addWidget(self.gmail_label)
        
        status_frame.setLayout(status_layout)
        layout.addWidget(status_frame)
        
    def create_generate_tab(self):
        """创建邮箱生成标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 生成邮箱组
        generate_group = QGroupBox("生成新邮箱")
        generate_layout = QGridLayout()
        
        # 应用名称
        generate_layout.addWidget(QLabel("应用名称:"), 0, 0)
        self.app_name_input = QLineEdit()
        self.app_name_input.setPlaceholderText("例如: wechat, alipay, taobao")
        generate_layout.addWidget(self.app_name_input, 0, 1)
        
        # 自定义前缀
        generate_layout.addWidget(QLabel("自定义前缀:"), 1, 0)
        self.prefix_input = QLineEdit()
        self.prefix_input.setPlaceholderText("例如: verify, test, demo")
        generate_layout.addWidget(self.prefix_input, 1, 1)
        
        # 自定义后缀
        generate_layout.addWidget(QLabel("自定义后缀:"), 2, 0)
        self.suffix_input = QLineEdit()
        self.suffix_input.setPlaceholderText("例如: prod, dev, staging")
        generate_layout.addWidget(self.suffix_input, 2, 1)
        
        # 生成按钮
        self.generate_btn = QPushButton("🔄 生成邮箱")
        self.generate_btn.clicked.connect(self.generate_email)
        generate_layout.addWidget(self.generate_btn, 3, 0, 1, 2)
        
        generate_group.setLayout(generate_layout)
        layout.addWidget(generate_group)
        
        # 结果显示
        result_group = QGroupBox("生成结果")
        result_layout = QVBoxLayout()
        
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(200)
        self.result_text.setReadOnly(True)
        result_layout.addWidget(self.result_text)
        
        result_group.setLayout(result_layout)
        layout.addWidget(result_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
        
    def create_code_tab(self):
        """创建验证码查询标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 查询组
        query_group = QGroupBox("验证码查询")
        query_layout = QGridLayout()
        
        # 邮箱ID
        query_layout.addWidget(QLabel("邮箱ID:"), 0, 0)
        self.email_id_input = QSpinBox()
        self.email_id_input.setMinimum(1)
        self.email_id_input.setMaximum(999999)
        query_layout.addWidget(self.email_id_input, 0, 1)
        
        # 包含已使用
        self.include_used_checkbox = QCheckBox("包含已使用的验证码")
        query_layout.addWidget(self.include_used_checkbox, 1, 0, 1, 2)
        
        # 查询按钮
        self.query_btn = QPushButton("🔍 查询验证码")
        self.query_btn.clicked.connect(self.query_code)
        query_layout.addWidget(self.query_btn, 2, 0, 1, 2)
        
        query_group.setLayout(query_layout)
        layout.addWidget(query_group)
        
        # 验证码显示
        code_group = QGroupBox("验证码信息")
        code_layout = QVBoxLayout()
        
        self.code_display = QTextEdit()
        self.code_display.setMaximumHeight(300)
        self.code_display.setReadOnly(True)
        code_layout.addWidget(self.code_display)
        
        code_group.setLayout(code_layout)
        layout.addWidget(code_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
        
    def create_manage_tab(self):
        """创建邮箱管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 控制面板
        control_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.load_emails)
        control_layout.addWidget(self.refresh_btn)
        
        # 过滤器
        control_layout.addWidget(QLabel("应用过滤:"))
        self.app_filter = QComboBox()
        self.app_filter.addItem("所有应用")
        self.app_filter.currentTextChanged.connect(self.filter_emails)
        control_layout.addWidget(self.app_filter)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 邮箱表格
        self.email_table = QTableWidget()
        self.email_table.setColumnCount(6)
        self.email_table.setHorizontalHeaderLabels([
            "ID", "邮箱地址", "应用名称", "前缀", "创建时间", "状态"
        ])
        self.email_table.setSelectionBehavior(QTableWidget.SelectRows)
        layout.addWidget(self.email_table)
        
        widget.setLayout(layout)
        return widget
        
    def create_monitor_tab(self):
        """创建系统监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 系统状态
        status_group = QGroupBox("系统状态")
        status_layout = QGridLayout()
        
        status_layout.addWidget(QLabel("API服务器:"), 0, 0)
        self.api_status_label = QLabel("检查中...")
        status_layout.addWidget(self.api_status_label, 0, 1)
        
        status_layout.addWidget(QLabel("Gmail监听:"), 1, 0)
        self.gmail_status_label = QLabel("检查中...")
        status_layout.addWidget(self.gmail_status_label, 1, 1)
        
        status_layout.addWidget(QLabel("邮箱总数:"), 2, 0)
        self.email_count_label = QLabel("0")
        status_layout.addWidget(self.email_count_label, 2, 1)
        
        status_layout.addWidget(QLabel("验证码总数:"), 3, 0)
        self.code_count_label = QLabel("0")
        status_layout.addWidget(self.code_count_label, 3, 1)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # 操作按钮
        action_group = QGroupBox("系统操作")
        action_layout = QHBoxLayout()
        
        self.check_emails_btn = QPushButton("📧 手动检查邮件")
        self.check_emails_btn.clicked.connect(self.check_emails)
        action_layout.addWidget(self.check_emails_btn)
        
        self.open_docs_btn = QPushButton("📚 打开API文档")
        self.open_docs_btn.clicked.connect(self.open_api_docs)
        action_layout.addWidget(self.open_docs_btn)
        
        action_group.setLayout(action_layout)
        layout.addWidget(action_group)
        
        # 日志显示
        log_group = QGroupBox("系统日志")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
        
    def generate_email(self):
        """生成邮箱"""
        app_name = self.app_name_input.text().strip()
        if not app_name:
            QMessageBox.warning(self, "警告", "请输入应用名称")
            return
            
        data = {"app_name": app_name}
        
        if self.prefix_input.text().strip():
            data["prefix"] = self.prefix_input.text().strip()
            
        if self.suffix_input.text().strip():
            data["custom_suffix"] = self.suffix_input.text().strip()
            
        try:
            response = requests.post(f"{self.api_base}/api/generate-email", json=data)
            result = response.json()
            
            if result.get("success"):
                self.result_text.setHtml(f"""
                <h3>✅ 邮箱生成成功！</h3>
                <p><strong>邮箱地址:</strong> <span style="color: blue; font-size: 14px;">{result.get('email_address')}</span></p>
                <p><strong>邮箱ID:</strong> {result.get('email_id')}</p>
                <p><strong>应用名称:</strong> {result.get('app_name')}</p>
                <p><strong>创建时间:</strong> {result.get('created_at')}</p>
                """)
                
                # 清空输入框
                self.app_name_input.clear()
                self.prefix_input.clear()
                self.suffix_input.clear()
                
                # 刷新邮箱列表
                self.load_emails()
                
            else:
                self.result_text.setHtml(f"""
                <h3>❌ 生成失败</h3>
                <p>{result.get('message', '未知错误')}</p>
                """)
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"请求失败: {str(e)}")
            
    def query_code(self):
        """查询验证码"""
        email_id = self.email_id_input.value()
        include_used = self.include_used_checkbox.isChecked()
        
        try:
            url = f"{self.api_base}/api/get-code/{email_id}"
            if include_used:
                url += "?include_used=true"
                
            response = requests.get(url)
            result = response.json()
            
            if result.get("success"):
                self.code_display.setHtml(f"""
                <h3>✅ 验证码获取成功！</h3>
                <p><strong>验证码:</strong> <span style="color: red; font-size: 24px; font-weight: bold;">{result.get('code')}</span></p>
                <p><strong>发件人:</strong> {result.get('sender_email', '未知')}</p>
                <p><strong>邮件主题:</strong> {result.get('subject', '无主题')}</p>
                <p><strong>提取时间:</strong> {result.get('extracted_at', '未知')}</p>
                <p><strong>过期时间:</strong> {result.get('expires_at', '未知')}</p>
                <p><strong>使用状态:</strong> {'已使用' if result.get('is_used') else '未使用'}</p>
                """)
            else:
                self.code_display.setHtml(f"""
                <h3>ℹ️ 暂无验证码</h3>
                <p>{result.get('message', '未找到验证码')}</p>
                """)
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"查询失败: {str(e)}")
            
    def load_emails(self):
        """加载邮箱列表"""
        try:
            response = requests.get(f"{self.api_base}/api/emails")
            result = response.json()
            
            if result.get("success"):
                emails = result.get("emails", [])
                
                # 更新表格
                self.email_table.setRowCount(len(emails))
                
                # 更新应用过滤器
                apps = set()
                
                for i, email in enumerate(emails):
                    self.email_table.setItem(i, 0, QTableWidgetItem(str(email.get('id'))))
                    self.email_table.setItem(i, 1, QTableWidgetItem(email.get('email_address', '')))
                    self.email_table.setItem(i, 2, QTableWidgetItem(email.get('app_name', '')))
                    self.email_table.setItem(i, 3, QTableWidgetItem(email.get('prefix', '')))
                    self.email_table.setItem(i, 4, QTableWidgetItem(email.get('created_at', '')))
                    
                    status = "✅ 活跃" if email.get('is_active') else "❌ 停用"
                    self.email_table.setItem(i, 5, QTableWidgetItem(status))
                    
                    apps.add(email.get('app_name', ''))
                
                # 更新过滤器
                current_filter = self.app_filter.currentText()
                self.app_filter.clear()
                self.app_filter.addItem("所有应用")
                for app in sorted(apps):
                    self.app_filter.addItem(app)
                
                # 恢复过滤器选择
                index = self.app_filter.findText(current_filter)
                if index >= 0:
                    self.app_filter.setCurrentIndex(index)
                
                # 更新统计
                self.email_count_label.setText(str(len(emails)))
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载邮箱列表失败: {str(e)}")
            
    def filter_emails(self):
        """过滤邮箱列表"""
        filter_text = self.app_filter.currentText()
        
        for i in range(self.email_table.rowCount()):
            if filter_text == "所有应用":
                self.email_table.setRowHidden(i, False)
            else:
                app_name = self.email_table.item(i, 2).text()
                self.email_table.setRowHidden(i, app_name != filter_text)
                
    def check_emails(self):
        """手动检查邮件"""
        try:
            response = requests.post(f"{self.api_base}/api/check-emails")
            result = response.json()
            
            if result.get("success"):
                processed = result.get("processed_count", 0)
                total = result.get("total_messages", 0)
                
                self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 邮件检查完成: {processed}/{total} 邮件处理")
                
                if processed > 0:
                    QMessageBox.information(self, "成功", f"成功处理 {processed} 封邮件")
                else:
                    QMessageBox.information(self, "信息", "没有新邮件需要处理")
            else:
                QMessageBox.warning(self, "警告", f"检查失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"检查邮件失败: {str(e)}")
            
    def open_api_docs(self):
        """打开API文档"""
        import webbrowser
        webbrowser.open(f"{self.api_base}/docs")
        
    def update_connection_status(self, connected):
        """更新连接状态"""
        if connected:
            self.connection_label.setText("🟢 API服务器: 已连接")
            self.api_status_label.setText("🟢 正常运行")
        else:
            self.connection_label.setText("🔴 API服务器: 未连接")
            self.api_status_label.setText("🔴 连接失败")
