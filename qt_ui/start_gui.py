#!/usr/bin/env python3
"""
Qt GUI启动脚本
"""

import sys
import subprocess
import os
from pathlib import Path

def check_pyqt5():
    """检查PyQt5是否已安装"""
    try:
        import PyQt5
        print("✅ PyQt5 已安装")
        return True
    except ImportError:
        print("❌ PyQt5 未安装")
        return False

def install_requirements():
    """安装Qt GUI依赖"""
    print("📦 安装Qt GUI依赖...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, cwd=Path(__file__).parent)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def check_api_server():
    """检查API服务器是否运行"""
    try:
        import requests
        response = requests.get("http://localhost:8000/health", timeout=2)
        if response.status_code == 200:
            print("✅ API服务器正在运行")
            return True
        else:
            print("❌ API服务器响应异常")
            return False
    except:
        print("❌ API服务器未运行")
        return False

def start_gui():
    """启动GUI应用"""
    print("🚀 启动Qt GUI应用...")
    
    # 检查PyQt5
    if not check_pyqt5():
        print("📦 正在安装PyQt5...")
        if not install_requirements():
            print("❌ 无法安装依赖，请手动安装: pip install PyQt5")
            return False
    
    # 检查API服务器
    if not check_api_server():
        print("⚠️  警告: API服务器未运行")
        print("请先启动API服务器: python src/api_server.py")
        
        response = input("是否继续启动GUI? (y/N): ")
        if response.lower() != 'y':
            return False
    
    # 启动GUI
    try:
        from main import main
        main()
        return True
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        return False

if __name__ == "__main__":
    print("📱 邮箱验证码管理系统 - Qt GUI")
    print("=" * 50)
    
    if start_gui():
        print("✅ GUI应用已退出")
    else:
        print("❌ GUI启动失败")
        sys.exit(1)
